package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseInfoSearchServlet2")
public class QueryUsecaseInfoSearchServlet2 extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseInfoSearchServlet2() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		String ptype = request.getParameter("ptype");
		String page = request.getParameter("pageIndex");
		String id = request.getParameter("search_data");
		String limit = request.getParameter("pageSize");
		id = java.net.URLDecoder.decode(id,"utf-8");
		
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		
		
		DaoCMCC dao = new DaoCMCC();
		
        JSONArray result = new JSONArray();
		JSONArray jsonArray0 = new JSONArray();
		int count=0;
		
		String sql8="SELECT usecaseid FROM iwork2.jobtestinfo;";
		ResultSet rs8 = dao.executeQuery(sql8);
		Set<String> paramedSet = new HashSet<String>();
		try {
			while(rs8.next()) {
				if(rs8.getString(1)==null) {
					
				}else {
					String temp = rs8.getString(1);
					String[] temps = temp.split("/");
					for(int i=0;i<temps.length;i++) {
						paramedSet.add(temps[i]);
					}
				}
			}
		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		

		if(id.equals("")) {
			//创建任务
			if(ptype.equals("1")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ApplicableVersion like '%V5.75.10%'  and state='就绪'    and  (requirecount is not null and requirecount>0) and hasautomated!='1-无法自动化' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);

				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						
						String tempfeature = tempJsonObject.getString("performancetopic");
						String temphasautomated = tempJsonObject.getString("hasautomated");

						if(tempfeature.equals("语音性能")||tempfeature.equals("KPI")||tempfeature.equals("TNR基础性能")||tempfeature.equals("高铁")||tempfeature.equals("IOT")) {
							if(temphasautomated.contains("已部署")) {
								tempJsonObject.put("parambushu", 1);
							}else {
								tempJsonObject.put("parambushu", 0);
							}
						
						}else {
							if(paramedSet.contains(tempJsonObject.getString("id")) ) {
								tempJsonObject.put("parambushu", 1);
							}else {
								tempJsonObject.put("parambushu", 0);
							}
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ApplicableVersion like '%V5.75.10%'  and state='就绪'    and  (requirecount is not null and requirecount>0) and hasautomated!='1-无法自动化';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

			}else if(ptype.equals("0")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0  and (state='就绪' or state='新建') and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0  and (state='就绪' or state='新建') and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("3")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+"  and (state='就绪' or state='新建') and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("2")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+"  and (state='就绪' or state='新建') and (hasautomated='2-已自动化' or hasautomated='5-已部署') and ApplicableVersion like '%V5.75.10%' and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and ApplicableVersion like '%V5.75.10%' and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}else {
			//创建任务
			if(ptype.equals("1")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=1 and ApplicableVersion like '%V5.75.10%'  and state='就绪'    and  (requirecount is not null and requirecount>0) and hasautomated!='1-无法自动化' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						
						String tempfeature = tempJsonObject.getString("performancetopic");
						String temphasautomated = tempJsonObject.getString("hasautomated");

						if(tempfeature.equals("语音性能")||tempfeature.equals("KPI")||tempfeature.equals("TNR基础性能")||tempfeature.equals("高铁")||tempfeature.equals("IOT")) {
							if(temphasautomated.contains("已部署")) {
								tempJsonObject.put("parambushu", 1);
							}else {
								tempJsonObject.put("parambushu", 0);
							}
						
						}else {
							if(paramedSet.contains(tempJsonObject.getString("id")) ) {
								tempJsonObject.put("parambushu", 1);
							}else {
								tempJsonObject.put("parambushu", 0);
							}
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=1 and ApplicableVersion like '%V5.75.10%'  and state='就绪'    and  (requirecount is not null and requirecount>0) and hasautomated!='1-无法自动化';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("0")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and  CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("3")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("2")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and ApplicableVersion like '%V5.75.10%' and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)  and (state='就绪' or state='新建') and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and ApplicableVersion like '%V5.75.10%' and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		JSONObject object = new JSONObject();
		object.put("code", 0);
		object.put("msg","");
		object.put("count1", count);
		object.put("data1", jsonArray0);
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




