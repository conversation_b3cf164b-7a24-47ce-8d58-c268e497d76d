package servlet4;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUseCaseInfoServlet")
public class QueryUseCaseInfoServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUseCaseInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		DaoCMCC dao = new DaoCMCC();

		String id = request.getParameter("id");


		JSONObject object = new JSONObject();

		String element = "";
		
        String updateSql="select featureid,mrid,testid,element from  usecaseinfo where id ='"+id+"'";
		ResultSet rs= dao.executeQuery(updateSql);
		System.out.println(updateSql);
		try {
			if(rs.next()) {
				object.put("featureid", rs.getString(1));
				object.put("mrid", rs.getString(2));
				object.put("testid", rs.getString(3));
				if(rs.getString(4)!=null) {
					element= rs.getString(4);
					object.put("element",element);
				}else {
					object.put("element", "");
				}

			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		JSONArray aaaaArray = new JSONArray();
		if(!element.equals("")) {
			JSONArray array = new JSONArray(element);
			for(int i=0;i<array.length();i++) {
				JSONObject tempObject = array.getJSONObject(i);
				if(!tempObject.isNull("id")) {
					if(!tempObject.isNull("children")) {
						JSONArray array2 = tempObject.getJSONArray("children");
						for(int j=0;j<array2.length();j++) {
							JSONObject tempObject2 = array2.getJSONObject(j);

							if(!tempObject2.isNull("id")) {
								if(!tempObject2.isNull("children")) {
									JSONArray array3 = tempObject2.getJSONArray("children");
									for(int k=0;k<array3.length();k++) {
										JSONObject tempObject3 = array3.getJSONObject(k);
										if(!tempObject3.isNull("id")) {
											aaaaArray.put(tempObject3.getString("id"));

										}
									}

								}else {
									aaaaArray.put(tempObject2.getString("id"));
								}
//								
							}
						}
					}else {
						aaaaArray.put(tempObject.getString("id"));
					}
				}

			}
		}
		object.put("temp", aaaaArray.toString());
		System.out.println(aaaaArray);
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




