package servlet5;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/UpdateSpaInfoServlet2")
public class UpdateSpaInfoServlet2 extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public UpdateSpaInfoServlet2() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		String id = request.getParameter("usecase");
		String a = request.getParameter("a");
		String b = request.getParameter("b");

		id = "ZXPFM-"+id;
		Date d = new Date();
	    SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    String updatetime=sbf.format(d);
	    
		DaoCMCC dao = new DaoCMCC();
		String sqString = "select id from rdctestcase where systemid = '"+id+"'";
		ResultSet rSet1 = dao.executeQuery(sqString);
		try {
			if(rSet1.next()) {
				id = rSet1.getString(1);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        JSONObject result = new JSONObject();
        //更改终端界限
	     String updateSql="insert into usecasespainfo(id,delayy,durationn,updatetime) values";
		 updateSql+= "('"+id+"','"+a+"','"+b+"','"+updatetime+"')";
	     updateSql+=" on duplicate key update id=values(id),delayy=values(delayy),durationn=values(durationn),updatetime=values(updatetime);";
//	     System.out.println(updateSql);
//        String sql="set sql_safe_updates=0;";
//        String sql1="update job set configuenum="+num+",updatetime='"+updatetime+"',grnum='"+grnum+"' where id='"+id+"';";
//        dao.execute(sql);
        dao.execute(updateSql);
        result.put("msg", "更新成功");
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




