package utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONObject;



public class CopyExecl {

	public static String copy(String key,String id) {
		File uploadPath = new File("//home//xuejie//iwork2//2CIAparamlisttemp");
		if(!uploadPath.exists()){
		//创建目录
			uploadPath.mkdir();
		}
		String ids[] = id.split("-");
		if(ids.length==1) {
			id = id.split("-")[0];
		}else{
			id = id.split("-")[1];
		}
        
		File directory= new File(uploadPath+"//"+id);
		if (!directory.exists()) {
		    directory.mkdirs();
		}
		
		String descfilename = directory+"//1.xlsx";

		File file=new File(key);
		File file2=new File(descfilename);
		if(file2.exists()) {
			file2.delete();
		}
		try {
			Files.copy(file.toPath(), file2.toPath());
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
//		execPythonFile(descfilename);
//		try {
//		InputStream fis = new FileInputStream(key);
//		FileOutputStream fos = new FileOutputStream(descfilename);
//		byte[] data = new byte[1444*100000];
//		int len=0;
//		while((len=fis.read(data))!=-1) {
//			fos.write(data,0,len);
//		}
//		fis.close();
//		fos.close();
//	} catch (IOException e) {
//		// TODO Auto-generated catch block
//		e.printStackTrace();
//	}
		return descfilename;
	}
	
	public static void execPythonFile(String fileName) {
		  // 鈶� 褰撳墠绯荤粺绫�
			Process proc=null;
			StringBuffer sb=new StringBuffer();
			String pypath = "D:\\iwork2\\paramcheck\\temppy\\test.py";
			String cmdString = "python "+pypath+" \""+fileName+"\"";
//			System.out.println(cmdString);
			try {
				proc = Runtime.getRuntime().exec(cmdString);
				InputStream is=proc.getInputStream();
				InputStreamReader isr=new InputStreamReader(is);
				MyThread2 myThread2 = new MyThread2(proc.getErrorStream());  
		        myThread2.start();
				BufferedReader reader=new BufferedReader(isr);
				String linestr=null; int i=0;
				while((linestr=reader.readLine())!=null) {
					System.out.println(linestr);
					if(0!=i)
						sb.append("\r\n");
					i++;
					sb.append(linestr);
				}
				try {
					if(null!=reader)
						reader.close();
					if(null!=isr)
						isr.close();
					if(null!=is)
						is.close();
					proc.waitFor();
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			if(proc!=null) {
				proc.destroy();
			}
		}
}

//class  MyThread2 extends Thread {
//	
//	private InputStream is;
//	public MyThread2(InputStream is) {
//		if(null==is) {
//			System.out.println("xuejie: the inputstream is null");
//		}
//		this.is = is;
//	}
//	@Override
//	public void run() {
//		// TODO Auto-generated method stub
//		if(this.is==null) 
//			return;
//		StringBuffer sb=new StringBuffer();
//		InputStreamReader ir = null;
//		BufferedReader br =null;
//		ir=new InputStreamReader(this.is); 
//		br = new BufferedReader(ir);
//		String linestr=null; int j=0;
//		try {
//			while((linestr=br.readLine())!=null) {
//				if(0!=j)
//					sb.append("\r\n");
//				j++;
//				sb.append(linestr);
//			}
//		} catch (IOException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}finally {
//				try {
//					if(null!=br)
//						br.close();
//					if(null!=ir)
//						ir.close();
//					if(null!=this.is)
//						this.is.close();
//				} catch (IOException e) {
//					// TODO Auto-generated catch block
//					e.printStackTrace();
//				}
//		}
//	} 	
//}

