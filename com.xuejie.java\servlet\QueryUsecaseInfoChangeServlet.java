package servlet;

import java.awt.Checkbox;
import java.awt.Desktop.Action;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.security.Policy;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Random;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.zip.GZIPInputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.html.parser.Entity;
import javax.websocket.Decoder.Binary;

import org.apache.commons.lang.math.RandomUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequestFactory;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.formula.DataValidationEvaluator.ValidationEnum;
import org.apache.tomcat.util.http.fileupload.FileUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.alibaba.fastjson.util.IOUtils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.mashape.unirest.request.GetRequest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseInfoChangeServlet")
public class QueryUsecaseInfoChangeServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseInfoChangeServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		String ptype = request.getParameter("ptype");
		
		String page = request.getParameter("page");
		String id = request.getParameter("id");
		String limit = request.getParameter("limit");
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		
		DaoCMCC dao = new DaoCMCC();
		
        JSONArray result = new JSONArray();
		JSONArray jsonArray0 = new JSONArray();
		int count=0;
		
		
		String sql8="select t.id from (SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as t join paraminfo as t2 on t.id=t2.id group by t2.id;";
		ResultSet rs8 = dao.executeQuery(sql8);
		Set<String> paramedSet = new HashSet<String>();
		String sql9="select t.id from (SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as t join casekpicount as t2 on t.id=t2.taskid group by t2.taskid;";
		ResultSet rs9 = dao.executeQuery(sql9);
		Set<String> ChooseedKpiSet = new HashSet<String>();
		try {
			while(rs8.next()) {
				paramedSet.add(rs8.getString(1));
			}
			while(rs9.next()) {
				ChooseedKpiSet.add(rs9.getString(1));
			}
		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		

		if(id.equals("")) {
			//创建任务
			if(ptype.equals("1")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and TestCaseApplicableVersion like '%V5.65.10%' and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='3-待自动化' or hasautomated ='4-待部署') and executephase='FRT' and ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%')) limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);

				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(ChooseedKpiSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("kpibushu", 1);
						}else {
							tempJsonObject.put("kpibushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and TestCaseApplicableVersion like '%V5.65.10%' and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='3-待自动化' or hasautomated ='4-待部署') and executephase='FRT' and ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%'));";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

			}else if(ptype.equals("0")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(ChooseedKpiSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("kpibushu", 1);
						}else {
							tempJsonObject.put("kpibushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}else {
			//创建任务
			if(ptype.equals("1")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=1 and TestCaseApplicableVersion like '%V5.65.10%' and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='3-待自动化' or hasautomated ='4-待部署') and executephase='FRT' and ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%')) limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(ChooseedKpiSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("kpibushu", 1);
						}else {
							tempJsonObject.put("kpibushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=1 and TestCaseApplicableVersion like '%V5.65.10%' and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='3-待自动化' or hasautomated ='4-待部署') and executephase='FRT' and ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%'));";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else if(ptype.equals("0")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}else {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(ChooseedKpiSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("kpibushu", 1);
						}else {
							tempJsonObject.put("kpibushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype="+ptype+" and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}

		result.put(ptype);
        result.put(jsonArray0);
        result.put(count);
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




