package utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.*;
import org.apache.commons.lang.StringUtils;
import org.apache.naming.java.javaURLContextFactory;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xdgf.usermodel.section.geometry.EllipticalArcTo;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;


public class WriteXlsx {
		 static Workbook wb;
		 static Sheet sheet;
		 Row row;

		 
	    public static void File(String filepath,String ManagedElement,String SubNetwork,String gNBId,String cellLocalId) {
	        if(filepath==null){
	            return;
	        }
	        File file = new File(filepath);
	        String ext = filepath.substring(filepath.lastIndexOf("."));
//	        System.out.println(ext);
	        try {
	            InputStream is = new FileInputStream(filepath);
	            if(".xls".equals(ext)){
	                wb = new HSSFWorkbook(is);
	            }else if(".xlsx".equals(ext)){
	                wb = new XSSFWorkbook(is);
	            }else{
	                wb=null;
	            }
				sheet = wb.getSheet("MEIDList");
				int count=0;
				
				Row row1 = sheet.createRow(4);
				for(Cell cell :sheet.getRow(3)) {
					
					int celltype = cell.getCellType();
					switch (celltype) {
						case 0:
							Double temp1 =  cell.getNumericCellValue();
							String temp = String.valueOf(temp1);
							break;
						case 1:
							String temp2 = cell.getStringCellValue();
							if(temp2.equals("SubNetwork")) {
								row1.createCell(count).setCellValue(SubNetwork);
							}else if(temp2.equals("gNBId")) {
								row1.createCell(count).setCellValue(gNBId);
							}else if(temp2.equals("ManagedElement")) {
								row1.createCell(count).setCellValue(ManagedElement);
							}else if(temp2.equals("cellLocalId")) {
								row1.createCell(count).setCellValue(cellLocalId);
							}
							break;

						case 2:
		
						case 3:
		
						case 4:
							
						case 5:						

					}
					count++;
				}				
		        FileOutputStream outputStream =new FileOutputStream(file);
                wb.write(outputStream);
                outputStream.flush();
                outputStream.close();
                wb.close();
//                execPythonFile(filepath);
	        } catch (FileNotFoundException e) {
	            e.printStackTrace();
	            System.out.println("读取文件失败");
	        } catch (IOException e) {
	            e.printStackTrace();
	            System.out.println("读取失败");
	        }
	    }
	 
		public static void execPythonFile(String fileName) {
			  // 鈶� 褰撳墠绯荤粺绫�
				Process proc=null;
				StringBuffer sb=new StringBuffer();
//				String pypath = "D:\\iwork2\\paramcheck\\temppy\\test.py";
				System.out.println(fileName);
				String pypath = "/home/<USER>/iwork2/temppy/test.py";
				System.out.println(pypath);

				String cmdString = "/usr/bin/python2.7 "+pypath+" \""+fileName+"\"";
				System.out.println(cmdString);
				try {
					proc = Runtime.getRuntime().exec(cmdString);
					InputStream is=proc.getInputStream();
					InputStreamReader isr=new InputStreamReader(is);
					MyThread2 myThread2 = new MyThread2(proc.getErrorStream());  
			        myThread2.start();
					BufferedReader reader=new BufferedReader(isr);
					String linestr=null; int i=0;
					while((linestr=reader.readLine())!=null) {
						System.out.println(linestr);
						if(0!=i)
							sb.append("\r\n");
						i++;
						sb.append(linestr);
					}
					try {
						if(null!=reader)
							reader.close();
						if(null!=isr)
							isr.close();
						if(null!=is)
							is.close();
						proc.waitFor();
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				} catch (IOException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
				if(proc!=null) {
					proc.destroy();
				}
			}

	    
	    
	    public static void main(String[] args) {
			String url ="D:\\Users\\10306639\\Desktop\\1.xlsx";
			File(url,"310993","16016","310993","119");
		}
}

class  MyThread2 extends Thread {
	
	private InputStream is;
	public MyThread2(InputStream is) {
		if(null==is) {
			System.out.println("xuejie: the inputstream is null");
		}
		this.is = is;
	}
	@Override
	public void run() {
		// TODO Auto-generated method stub
		if(this.is==null) 
			return;
		StringBuffer sb=new StringBuffer();
		InputStreamReader ir = null;
		BufferedReader br =null;
		ir=new InputStreamReader(this.is); 
		br = new BufferedReader(ir);
		String linestr=null; int j=0;
		try {
			while((linestr=br.readLine())!=null) {
				if(0!=j)
					sb.append("\r\n");
				j++;
				sb.append(linestr);
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally {
				try {
					if(null!=br)
						br.close();
					if(null!=ir)
						ir.close();
					if(null!=this.is)
						this.is.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
		}
	} 	
}

