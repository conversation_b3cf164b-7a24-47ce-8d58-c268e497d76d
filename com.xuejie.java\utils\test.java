package utils;



import java.sql.ResultSet;
import java.sql.SQLException;

import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;


public class test {
	public static void main(String[] args) {
		DaoCMCC dao = new DaoCMCC();
	    String sql2="set sql_safe_updates=0;";
		dao.execute(sql2);
		String sql = "SELECT gnbidinfo FROM iwork2.poolinfo;";
		ResultSet rs = dao.executeQuery(sql);
		try {
			while(rs.next()) {
				String tempString = rs.getString(1);
				String[] gnbinfo = tempString.split("/");
				for(String s:gnbinfo) {
					System.out.println(s);
					String sql1="update cienvinfo set isshare='1' where neip='"+s+"';";
	        		System.out.println(sql1);
	        		dao.execute(sql1);
				}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		dao.close();
	}
	
	
	private static JSONArray query(String element) {
		// TODO Auto-generated method stub
		JSONObject body = new JSONObject(element);
		String name="";
		JSONArray array = new JSONArray();
		try {
			String url ="https://wxiot.zte.com.cn/iwork/case2tem/";
//	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .body(body)
	                .asJson(); 
	        array = httpresponse.getBody().getArray();

	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return array; 
	}
}


