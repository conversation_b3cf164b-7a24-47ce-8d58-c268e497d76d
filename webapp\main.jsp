<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<meta http-equiv="Cache-control" content="no-cache">
<meta http-equiv="Cache" content="no-cache">
<title>iWork自动化测试</title>
<meta name="author" content="xuejie10170635">
<link href="css/bootstrap.min.css" rel="stylesheet">
<link href="css/materialdesignicons.min.css" rel="stylesheet">
<!-- <link href="css/layui.css" type="text/css" rel="stylesheet"> -->
<link href="css/style.min.css" rel="stylesheet">
<script type="text/javascript" src="res/js/highcharts.js"></script>
<link href="css/layer/layer.css" type="text/css" rel="stylesheet">
<link href="addtask/layui-v2.8.3/layui/css/layui.css" type="text/css" rel="stylesheet">


</head>
  
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-sm-6 col-md-3">
	<div class="card">
      <div class="card bg-primary">
        <div class="card-header clearfix">
          <div class="pull-right">
            <p class="h4 text-white m-t-0">运行时长(分钟)</p>
            <p class="h3 text-white m-b-0 fa-1-5x" id="info1_1"></p>
<!--             <p class="h4 text-white m-t-0">运行时长</p> -->
<!--             <p class="h4 text-white m-b-0 fa-1-5x" id="info1_2"></p> -->
          </div>
          <div  class="pull-left"> <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-history fa-1-5x"></i></span> </div>
        </div>
      </div>
	  <div class="card-body">
          <p id="info1_2">执行任务：XXX</p>
      </div>
	  </div>
    </div>
    
    <div class="col-sm-6 col-md-3">
	  <div class="card">
      <div class="card bg-danger">
        <div class="card-header clearfix">
          <div class="pull-right">
            <p class="h4 text-white m-t-0">V5.85.10测试用例数</p>
            <p class="h3 text-white m-b-0 fa-1-5x" id="info2_1">920,000</p>
          </div>
          <div class="pull-left"> <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-account fa-1-5x"></i></span> </div>
        </div>
      </div>
	  <div class="card-body">
          <p  id="info2_2"> &nbsp</p>
      </div>
	  </div>
    </div>
    
    <div class="col-sm-6 col-md-3">
	  <div class="card">
      <div class="card bg-success">
        <div class="card-header clearfix">
          <div class="pull-right">
            <p class="h4 text-white m-t-0">测试环境数</p>
            <p class="h3 text-white m-b-0 fa-1-5x" id="info3_1">34,005,000</p>
          </div>
          <div class="pull-left"> <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-arrow-down-bold fa-1-5x"></i></span> </div>
        </div>
      </div>
	  <div class="card-body">
          <p id="info3_2">人均维护环境：XXX</p>
      </div>
	  </div>
    </div>
    
    <div class="col-sm-6 col-md-3">
	  <div class="card">
      <div class="card bg-purple" ondblclick="queryinfo()">
<!--       ondblclick="queryinfo()" -->
        <div class="card-header clearfix">
          <div class="pull-right">
            <p style="float: right" class="h4 text-white m-t-0">V5.85.10用例执行率<br></p>
            <p class="h3 text-white m-b-0 fa-1-5x" id="info4_1">86.83%</p>
          </div>
          <div class="pull-left"> <span class="img-avatar img-avatar-48 bg-translucent"><i class="mdi mdi-comment-outline fa-1-5x"></i></span> </div>
        </div>
      </div>
	  <div class="card-body">
<!--           <p id="info4_2">测试成功率：XXX</p> -->
      </div>
	  </div>
    </div>
  </div>
  <div class="row">
  
                      <a onclick="addtk2()" style="display:inline-block;float:right;margin-top: -1%;color: white" class="layui-btn-normal layui-btn-radius layui-btn-sm" data-animation="fadeInUp" data-delay=".6s">iWork自动化测试工作大屏</a>
  
  </div>
  <div class="row">
    
    <div class="col-md-6"> 
      <div class="card">
        <div class="card-header">
          <h4>池化环境监控</h4>
        </div>
        <div class="card-body">
				<div id="chart1"> </div><br>
        </div>
      </div>
    </div>
    <div class="col-md-6"> 
      <div class="card">
        <div class="card-header">
          <h4>整体自动化率 
          </h4>
          
        </div>
        <div class="card-body">
        	<div id="chart2"> </div><br>
        </div>
      </div>
    </div>
  </div>
  
<div  class="table-responsive no-padding" >
  	<div style="width:100%;display:block;">
	<table class="table table-striped table-bordered taxt-nowrap rowSameHeight zero-configuration" style="table-layout:fixed;word-break:break-all;">
	<thead>
		<tr><th colspan="12" style="text-align:center;font-size:18px;color:white;overflow: hidden;white-space: nowrap;" bgcolor="#33cabb" >iChange EC</th></tr>
		<tr style="width: 100%">
			<th title="1CI" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;"bgcolor="#31bdec">1CI</th>
			<th id="info1" title="1CI" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>
			<th title="2CI" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;"bgcolor="#31bdec" >2CI</th>
			<th id="info2" title="2CI" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>
			<th title="3CI特性" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;" bgcolor="#31bdec">3CI特性</th>
			<th id="info3" title="3CI特性" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>
			<th title="3CI冒烟" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;"bgcolor="#31bdec">3CI冒烟</th>
			<th id="info4" title="3CI冒烟" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>
			<th title="平台故障" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;"bgcolor="#31bdec">平台故障</th>
			<th id="info5" title="平台故障" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>
			<th title="总计" style="color:white;text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;"bgcolor="#31bdec">总计</th>	
			<th id="info6" title="总计" style="text-align:center;font-size:15px;overflow: hidden;white-space: nowrap;">0</th>		
		</tr>
	</table>
  </div>
</div>
  
  <div  class="table-responsive no-padding" >
  	<div style="width:100%;display:block;">

  	<!-- 版本选择下拉框 -->
  	<div style="margin-bottom: 10px; text-align: right;">
  		<label for="versionSelect" style="margin-right: 10px; font-weight: bold;">版本选择：</label>
  		<select id="versionSelect" onchange="changeVersion()" style="padding: 5px 10px; border: 1px solid #ccc; border-radius: 4px; background-color: white;">
  			<option value="V5.85.10" selected>V5.85.10</option>
  			<option value="V5.75.20">V5.75.20</option>
  		</select>
  	</div>

	<table class="table table-striped table-bordered taxt-nowrap rowSameHeight zero-configuration"
                      style="table-layout:fixed;word-break:break-all;"  >
	<thead>
		<tr><th colspan="10" style="text-align:center;font-size:18px;color:white;overflow: hidden;white-space: nowrap;" bgcolor="#33cabb" ><span id="tableTitle">V5.85.10特性组用例</span></th></tr>
		<tr bgcolor="#33cabb" style="width: 100%">
			<th title="特性组" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;">特性组</th>
			<th title=自动化率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >用例自动化率</th>
			<th title="匹配率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >匹配率</th>		
			<th title="执行率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行率</th>	
			
			
			
			<th title="需求/特性用例数" style="color:white;text-align:center;font-size:13px;width: 25%;overflow: hidden;white-space: nowrap;" >需求/特性用例数</th>
			
			<th title="需求/特性用例数" style="color:white;text-align:center;font-size:13px;width: 25%;overflow: hidden;white-space: nowrap;" >可自动化用例数</th>
<!-- 		<th title="执行率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行率</th> -->
								<th title="执行用例数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行用例数</th>		
								<th title="执行用例频次" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行用例频次</th>		
								<th title="报告生成数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >报告生成数</th>		
								<th title="报告生成率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >报告生成率</th>		

<!-- 			<th title="回归用例数" style="color:white;text-align:center;font-size:13px;width: 6%;overflow: hidden;white-space: nowrap;" >回归用例数</th> -->
<!-- 			<th title="回归用例占比" style="color:white;text-align:center;font-size:13px;width: 6%;overflow: hidden;white-space: nowrap;" >回归用例占比</th> -->
<!-- 			<th title="回归用例自动化率" style="color:white;text-align:center;font-size:13px;width: 8%;overflow: hidden;white-space: nowrap;" >回归用例自动化率</th> -->
<!-- 			<th title="回归用例已自动化数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;">回归用例已自动化数</th> -->
<!-- 			<th title="回归用例待自动化数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >回归用例待自动化数</th> -->
<!-- 			<th title="回归用例不可自动化数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >回归用例不可自动化数</th> -->
			
		</tr>
	</thead>
	<tbody id="tbody2">
			</tbody>
</table>
  </div>
  </div>
  
  
<!-- <!--   	团队 --> 
<div  class="table-responsive no-padding" >
  	<div style="width:100%;display:none;">
		<table class="table table-striped table-bordered taxt-nowrap rowSameHeight zero-configuration"
                      style="table-layout:fixed;word-break:break-all;"  >
			<thead>
				<tr><th colspan="6" style="text-align:center;font-size:18px;color:white;overflow: hidden;white-space: nowrap;" bgcolor="#33cabb" >团队用例</th></tr>
				<tr bgcolor="#33cabb" style="width: 100%">
					<th title="团队" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;">团队</th>
					<th title="需求/特性用例数" style="color:white;text-align:center;font-size:13px;width: 30%;overflow: hidden;white-space: nowrap;" >需求/特性用例数</th>
					
					<th title="需求/特性用例数" style="color:white;text-align:center;font-size:13px;width: 30%;overflow: hidden;white-space: nowrap;" >自动化用例数</th>
					<th title=自动化率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >自动化率</th>
					<th title="执行用例数" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行用例数</th>		
					<th title="执行率" style="color:white;text-align:center;font-size:13px;width: 10%;overflow: hidden;white-space: nowrap;" >执行率</th>		
				
				</tr>
			</thead>
			<tbody id="tbody3">
			</tbody>
	</table>
  </div>
  </div>
</div>

<script type="text/javascript" src="addtask/layui-v2.8.3/layui/layui.js"></script>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/main.min.js"></script>
<script type="text/javascript" src="js/main.js?v=37"></script>
<script type="text/javascript">
      init()
</script>
</body>
</html>