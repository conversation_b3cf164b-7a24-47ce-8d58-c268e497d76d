package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.map.HashedMap;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseInfoSearchServlet")
public class QueryUsecaseInfoSearchServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseInfoSearchServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		String ptype = request.getParameter("ptype");
		String page = request.getParameter("pageIndex");
		String id = request.getParameter("search_data");
		String limit = request.getParameter("pageSize");
		id = java.net.URLDecoder.decode(id,"utf-8");
		String temptempversion = request.getParameter("version");
		String version = "";
		if(temptempversion==null) {
			
		}else {
			version = temptempversion;
		}
		String tempversion2 = request.getParameter("version2");
		String version2 = "";
		if(tempversion2==null) {
			
		}else {
			version2 = tempversion2;
		}
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		
		DaoCMCC dao = new DaoCMCC();
		
        JSONArray result = new JSONArray();
		JSONArray jsonArray0 = new JSONArray();
		int count=0;
		
		
		String sql8="select t.id from (SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as t join paraminfo as t2 on t.id=t2.id group by t2.id;";
		ResultSet rs8 = dao.executeQuery(sql8);
		Set<String> paramedSet = new HashSet<String>();

		try {
			while(rs8.next()) {
				paramedSet.add(rs8.getString(1));
			}

		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		

		if(id.equals("")) {
			//创建任务
			if(ptype.equals("1")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1  and ApplicableVersion like '%"+version+"%' and  ((ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ) order by CreatedDate desc limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ApplicableVersion like '%"+version+"%' and  ((ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' );";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				for(int i=0;i<jsonArray0.length();i++) {
					JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
					if(!tempJsonObject.isNull("ApplicableVersion")) {
						String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
						if(ApplicableVersion.contains("V5.75.10")) {
							tempJsonObject.put("version", "V5.75.10");
						}
						if(ApplicableVersion.contains("V5.75.20")) {
							tempJsonObject.put("version", "V5.75.20");
						}
						if(ApplicableVersion.contains("V5.85.10")) {
							tempJsonObject.put("version", "V5.85.10");
						}
					}
				}
			}else if(ptype.equals("0")){
				//创建任务
				String sql0="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%1级CI%' order by CreatedDate desc limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql0);
				String sql4="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%1级CI%';";
				ResultSet rs4 = dao.executeQuery(sql4);
				//分数以及是否通过
				String sql1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore ;";
				ResultSet rs1 = dao.executeQuery(sql1);
				Map<String, String> ifpassMap = new HashedMap();
				String sql1_1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore2 ;";
				ResultSet rs1_1 = dao.executeQuery(sql1_1);
				Map<String, String> ifpassMap2 = new HashedMap();
				//版本
				String sql2 = "SELECT gnbid,version FROM iwork2.gnbinfo;";
				ResultSet rs2 = dao.executeQuery(sql2);
				Map<String, String> versionMap = new HashedMap();
				int ptype0=0;
				try {
					
					
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					if(rs4.next()) {
						ptype0 = rs4.getInt(1);
					}
					while(rs2.next()) {
						String tempgnbid = rs2.getString(1);
						String tempversion = rs2.getString(2);
						versionMap.put(tempgnbid, tempversion);
					}
					
					while(rs1.next()) {
						String tempgnbid = rs1.getString(1);
						String tempversion = rs1.getString(2);
						ifpassMap.put(tempgnbid, tempversion);
					}
					
					while(rs1_1.next()) {
						String tempgnbid = rs1_1.getString(1);
						String tempversion = rs1_1.getString(2);
						ifpassMap2.put(tempgnbid, tempversion);
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				for(int i=0;i<jsonArray0.length();i++) {
					JSONObject tempObject = jsonArray0.getJSONObject(i);
					if(!tempObject.isNull("title")) {
						String temptitle = tempObject.getString("title");
						String tempgnbid = temptitle.split("-")[0];
						if(versionMap.containsKey(tempgnbid)) {
							tempObject.put("gnbversion", versionMap.get(tempgnbid));
						}else {
							tempObject.put("gnbversion", "");
						}
						String tempdevandver = temptitle.split("-")[temptitle.split("-").length-1];
						if(ifpassMap.containsKey(tempdevandver)) {
							tempObject.put("ifpass", ifpassMap.get(tempdevandver));
						}else {
							tempObject.put("ifpass", "");
						}
						if(ifpassMap2.containsKey(tempdevandver)) {
							tempObject.put("ifpass2", ifpassMap2.get(tempdevandver));
						}else {
							tempObject.put("ifpass2", "");
						}
					}
				}
				count= ptype0;

			}
			else if(ptype.equals("4")) {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and  ptype=4  limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and  ptype=4;";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			else {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+" and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%' order by CreatedDate desc limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(!tempJsonObject.isNull("ApplicableVersion")) {
							String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
							if(ApplicableVersion.contains("V5.75.10")) {
								tempJsonObject.put("version", "V5.75.10");
							}
							if(ApplicableVersion.contains("V5.75.20")) {
								tempJsonObject.put("version", "V5.75.20");
							}
							if(ApplicableVersion.contains("V5.85.10")) {
								tempJsonObject.put("version", "V5.85.10");
							}
						}
						
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype="+ptype+" and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

			}
		}else {
			//创建任务
			if(ptype.equals("1")) {
				id = id.replace("，", ",");

				String[] tempids = id.split(",");
				String sqlinsert = "";
				for(int i=0;i<tempids.length;i++) {
					if(i==0) {
						sqlinsert += "  CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase,ApplicableVersion,systemid,mrids,featureids) like  '%"+tempids[i]+"%'";
					}else {
						sqlinsert += " or CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase,ApplicableVersion,systemid,mrids,featureids) like  '%"+tempids[i]+"%'";
					}
				}
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ("+sqlinsert+") and ApplicableVersion like '%"+version+"%'  and ((ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ) order by CreatedDate desc limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {

					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ("+sqlinsert+") and ApplicableVersion like '%"+version+"%'   and  ((ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' );";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				for(int i=0;i<jsonArray0.length();i++) {
					JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
					if(!tempJsonObject.isNull("ApplicableVersion")) {
						String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
						if(ApplicableVersion.contains("V5.75.10")) {
							tempJsonObject.put("version", "V5.75.10");
						}
						if(ApplicableVersion.contains("V5.75.20")) {
							tempJsonObject.put("version", "V5.75.20");
						}
						if(ApplicableVersion.contains("V5.85.10")) {
							tempJsonObject.put("version", "V5.85.10");
						}
					}
				}
			}
			
			else if(ptype.equals("0")){
				//创建任务
				String sql0="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%1级CI%' order by CreatedDate desc";
				ResultSet rs0 = dao.executeQuery(sql0);
				//分数以及是否通过
				String sql1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore ;";
				ResultSet rs1 = dao.executeQuery(sql1);
				Map<String, String> ifpassMap = new HashedMap();
				String sql1_1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore2 ;";
				ResultSet rs1_1 = dao.executeQuery(sql1_1);
				Map<String, String> ifpassMap2 = new HashedMap();
				//版本
				String sql2 = "SELECT gnbid,version FROM iwork2.gnbinfo;";
				ResultSet rs2 = dao.executeQuery(sql2);
				Map<String, String> versionMap = new HashedMap();
				int ptype0=0;
				JSONArray jsonArray0_0 = new JSONArray();

				try {
					
					jsonArray0_0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
//					if(rs4.next()) {
//						ptype0 = rs4.getInt(1);
//					}
					while(rs2.next()) {
						String tempgnbid = rs2.getString(1);
						String tempversion = rs2.getString(2);
						versionMap.put(tempgnbid, tempversion);
					}
					
					while(rs1.next()) {
						String tempgnbid = rs1.getString(1);
						String tempversion = rs1.getString(2);
						ifpassMap.put(tempgnbid, tempversion);
					}
					
					while(rs1_1.next()) {
						String tempgnbid = rs1_1.getString(1);
						String tempversion = rs1_1.getString(2);
						ifpassMap2.put(tempgnbid, tempversion);
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				for(int i=0;i<jsonArray0_0.length();i++) {
					JSONObject tempObject = jsonArray0_0.getJSONObject(i);
					if(!tempObject.isNull("title")) {
						String temptitle = tempObject.getString("title");
						String tempgnbid = temptitle.split("-")[0];
						if(versionMap.containsKey(tempgnbid)) {
							tempObject.put("gnbversion", versionMap.get(tempgnbid));
						}else {
							tempObject.put("gnbversion", "");
						}
						String tempdevandver = temptitle.split("-")[temptitle.split("-").length-1];
						if(ifpassMap.containsKey(tempdevandver)) {
							tempObject.put("ifpass", ifpassMap.get(tempdevandver));
						}else {
							tempObject.put("ifpass", "");
						}
						if(ifpassMap2.containsKey(tempdevandver)) {
							tempObject.put("ifpass2", ifpassMap2.get(tempdevandver));
						}else {
							tempObject.put("ifpass2", "");
						}
					}
					
					if(tempObject.toString().contains(id)) {
						jsonArray0.put(tempObject);
					}

					        
				}
				 count = jsonArray0.length();
				JSONArray subArray = new JSONArray();
				if((limit1+limit2)<count ) {
					for (int i = limit1; i < (limit1+limit2); i++) {
			            subArray.put(jsonArray0.get(i));
			        }
				}else {
					for (int i = limit1; i < count; i++) {
			            subArray.put(jsonArray0.get(i));
			        }
				}
		        
		        jsonArray0 = subArray;
		       
			}
			
			else if(ptype.equals("4")){
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=4  limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase) like  '%"+id+"%' and ptype=4;";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			else {
				String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase,systemid,scene) like  '%"+id+"%' and ptype="+ptype+" and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%' order by CreatedDate desc limit "+limit1+","+limit2+";";
				ResultSet rs0 = dao.executeQuery(sql1);
				try {
					jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
					for(int i=0;i<jsonArray0.length();i++) {
						JSONObject tempJsonObject = jsonArray0.getJSONObject(i);
						if(paramedSet.contains(tempJsonObject.getString("id")) ) {
							tempJsonObject.put("parambushu", 1);
						}else {
							tempJsonObject.put("parambushu", 0);
						}
						
						if(!tempJsonObject.isNull("ApplicableVersion")) {
							String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
							if(ApplicableVersion.contains("V5.75.10")) {
								tempJsonObject.put("version", "V5.75.10");
							}
							if(ApplicableVersion.contains("V5.75.20")) {
								tempJsonObject.put("version", "V5.75.20");
							}
							if(ApplicableVersion.contains("V5.85.10")) {
								tempJsonObject.put("version", "V5.85.10");
							}
						}
					}
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String sql2="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and CONCAT(team,id,title,state,hasautomated,author,performancetopic,executephase,systemid,scene) like  '%"+id+"%' and ptype="+ptype+" and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%';";
				ResultSet rs2 = dao.executeQuery(sql2);
				try {
					if(rs2.next()) {
						count=rs2.getInt(1);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		JSONObject object = new JSONObject();
		object.put("code", 0);
		object.put("msg","");
		object.put("count1", count);
		object.put("data1", jsonArray0);
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




