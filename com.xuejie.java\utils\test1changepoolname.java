package utils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;

import dao.DaoCMCC;

public class test1changepoolname {
	public static void main(String[] args) {
		DaoCMCC dao = new DaoCMCC();
		Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd");
	    String updatetime=sbf.format(d);

        String Sql="SELECT a.jobid,b.jobname FROM iwork2.poolteminfo as a join job as b where a.jobid = b.id;";
        ResultSet rs = dao.executeQuery(Sql);
        try {
			while(rs.next()) {
				String tempString = rs.getString(1);
				String tempString1 = rs.getString(2);
//				System.out.println(tempString);
//				System.out.println(tempString1);
				String sql2 = "update poolteminfo set jobname = '"+tempString1+"' where jobid='"+tempString+"'";
				System.out.println(sql2);
				dao.execute(sql2);
//				String sql2 = ""
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        dao.close();
        
	}
}
