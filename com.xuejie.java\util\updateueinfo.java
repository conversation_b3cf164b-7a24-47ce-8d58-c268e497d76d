package util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;

import dao.DaoCMCC;

public class updateueinfo {
	private HttpClient customHttpClient=null; 
	public void  query(String proxy,String proxyname, String mcpip){
		// TODO Auto-generated method stub
		intihttpclient();
		
	    Date d = new Date();
	    SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    String updatetime=sbf.format(d);
	    
//	    String url2="http://"+proxy+":8080/iWork2Agent/getdevices?mcpip="+mcpip+"";
//	    Unirest.setHttpClient(customHttpClient);
//        HttpResponse<JsonNode> httpresponse2;
//        JSONArray array = new JSONArray();
//		try {
//			httpresponse2 = Unirest.get(url2)
//			        .asJson();
//			array =httpresponse2.getBody().getArray(); 
//		} catch (UnirestException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
		
      JSONArray array = new JSONArray();

	    String url2="http://"+proxy+":8080/iWork2Agent/getairplanemode?mcpip="+mcpip+"";
	    Unirest.setHttpClient(customHttpClient);
        HttpResponse<JsonNode> httpresponse2;
		try {
			httpresponse2 = Unirest.get(url2)
			        .asJson();
			array =  httpresponse2.getBody().getArray();
		} catch (UnirestException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
	    String url3="http://"+proxy+":8080/iWork2Agent/getipaddr?mcpip="+mcpip+"";		
	    Unirest.setHttpClient(customHttpClient);
        HttpResponse<JsonNode> httpresponse3;
		try {
			httpresponse3 = Unirest.get(url3)
			        .asJson();
			JSONArray jsonArray =  httpresponse3.getBody().getArray();
			for(int i=0;i<array.length();i++) {
				JSONObject object1 = array.getJSONObject(i);
				for(int j=0;j<jsonArray.length();j++) {
					JSONObject object2 = jsonArray.getJSONObject(j);
					if (object1.getString("adbid").equals(object2.getString("adbid"))) {
						object1.put("proxy", proxy);
						object1.put("mcpip", mcpip);
						object1.put("ipaddr", object2.getString("ipaddr"));
					}
				}
			}

		} catch (UnirestException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
	    String url4="http://"+proxy+":8080/iWork2Agent/getcellinfo?mcpip="+mcpip+"";
	    Unirest.setHttpClient(customHttpClient);
        HttpResponse<JsonNode> httpresponse4;
		try {
			httpresponse4 = Unirest.get(url4)
			        .asJson();
			JSONArray jsonArray =  httpresponse4.getBody().getArray();
			for(int i=0;i<array.length();i++) {
				JSONObject object1 = array.getJSONObject(i);
				for(int j=0;j<jsonArray.length();j++) {
					JSONObject object2 = jsonArray.getJSONObject(j);
					if (object1.getString("adbid").equals(object2.getString("adbid"))) {
						object1.put("proxy", proxy);
						object1.put("mcpip", mcpip);
						String cellinfo = object2.getString("cellinfo");
						String[] infos = cellinfo.split("_");
						if(infos.length>=6) {
							object1.put("cell", infos[0]);
							object1.put("rsrp", infos[3]);
							object1.put("sinr", infos[5]);
						}else {
							object1.put("cell", "");
							object1.put("rsrp", "");
							object1.put("sinr", "");
						}
					}
				}
			}

		} catch (UnirestException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		
		
		if(array.length()>1) {
	        String updateSql="insert into ueinfo(proxyip,agentname,mcpip,uenumber,brand,type,status,updatetime,ipaddr,cell,rsrp,sinr) values";
			for(int i=0;i<array.length();i++) {
				JSONObject obj =  array.getJSONObject(i);
				if(obj.isNull("ipaddr")) obj.put("ipaddr", "");
				if(obj.isNull("cell")) obj.put("cell", "");
				if(obj.isNull("rsrp")) obj.put("rsrp", "");
				if(obj.isNull("sinr")) obj.put("sinr", "");
				updateSql+= "('"+obj.getString("proxy")+"','"+proxyname+"','"+mcpip+"','"+obj.getString("adbid")+"','"+obj.getString("brand")+"','"+obj.getString("type")+"','"+obj.getString("airplaneMode")+"','"+updatetime+"','"+obj.getString("ipaddr")+"','"+obj.getString("cell")+"','"+obj.getString("rsrp")+"','"+obj.getString("sinr")+"'),";
		    }
			updateSql = updateSql.substring(0,updateSql.length()-1);
		    updateSql+=" on duplicate key update proxyip=values(proxyip),agentname=values(agentname),mcpip=values(mcpip),uenumber=values(uenumber),brand=values(brand),type=values(type),status=values(status),updatetime=values(updatetime),ipaddr=values(ipaddr),cell=values(cell),rsrp=values(rsrp),sinr=values(sinr);";
		    DaoCMCC dao=new DaoCMCC();
		    dao.execute(updateSql);
		    dao.close();
		}
	}

	
	
	protected void intihttpclient() { //��ʼ��http�ͻ��ˣ��ر�SSL��Ȩ
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
}
