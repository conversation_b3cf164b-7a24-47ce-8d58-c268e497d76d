package servlet;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.TreeSet;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;




/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryIchangeServlet")
public class QueryIchangeServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private  HttpClient customHttpClient=null;   
	 private MyThread2 myThread; 
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryIchangeServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
    
	@Override
	public void init() throws ServletException {
		// TODO Auto-generated method stub
//		super.init();
		 System.out.println("开始");
		 this.timerTask();
	}


	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		
		intihttpclient();
		String url = "https://icosg.dt.zte.com.cn/ZXRDCloud/RDCloud/WIC/rest/workspaces/RAN/queries/query_work_items";
		String sql_tp = "select * from teampeople";
		DaoCMCC dao = new DaoCMCC();
		ResultSet rs = dao.executeQuery(sql_tp);
		JSONArray array_tp = new JSONArray();
		try {
			array_tp = ResultSetToJsonArray.resultSetToJsonArray(rs);
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
	    QueryIchangeinfo( url,"1",array_tp);
		System.out.println("结束");
	   
    
	   
		
	}
	
	protected void intihttpclient() { 
		try {
		SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
            public boolean isTrusted(X509Certificate[] chain, String authType) {
                return true;
            }
        }).build();
        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
        Unirest.setHttpClient(customHttpClient);
    } catch (Exception e) {
    	e.printStackTrace();
	}
}

	protected void QueryIchangeinfo(String url,String ptype,JSONArray array_tp) {	
		JSONObject obj=new JSONObject("{\r\n"
				+ "    \"wiql\": \"select System_Id,System_Title,System_State,CreatorDepartment,System_CreatedDate,"
				+ "System_CreatedBy from chgRequest where System_WorkspaceKey='RAN'and"
				+ " CreatorDepartment = '性能研发四部/无线及算力网络研究院/系统产品_无线及算力产品经营部/中兴通讯股份有限公司'"
				+ " or DiscoveryActivity = 'FRT版本' limit 1000 order by System_CreatedDate DESC\"\r\n"
				+ "}");
		

		try {

			Unirest.setHttpClient(customHttpClient);
	        System.out.println("开始");
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "*/*")
	        	    .header("X-Tenant-Id","ZTE")
	        	    .header("X-Emp-No","10164320")
	        	    .header("X-Lang-Id","zh_CN")
	        	    .header("appcode","1a759b247a2942a891b1e29106fe3a1a")
	        	    .header("X-Auth-Value","b30e34ee0fc95a0484f506d61e8b9663")
	        	    .body(obj)
	                .asJson(); 
//	        System.out.println(httpresponse.getBody().toString());
		     String updateSql="insert into ichange_ec (id,title,state,creator,number,team,type,creatDate) values";
		     if(httpresponse.getBody().getObject().getJSONObject("bo").getJSONObject("result").getJSONArray("items").length()<2) {
		    	 System.out.println("拉取数据失败");
//		    	 System.out.println(httpresponse.getBody().toString());
		     }else {
		    	 JSONArray array = httpresponse.getBody().getObject().getJSONObject("bo").getJSONObject("result").getJSONArray("items");
			     for(int i=0;i<array.length();i++)
			     {
			    	 String id="";
			    	 String title="";
			    	 String state="";
			    	 String creator="";
			    	 String number="";
			    	 String team="";
			    	 String type = "";
			    	 String creatDate = "";
			    	 if(!array.getJSONObject(i).isNull("System_Id")) {
			    		 id = array.getJSONObject(i).getString("System_Id");
			    	 }
			    	 if(!array.getJSONObject(i).isNull("System_Title")) {
			    		 title = array.getJSONObject(i).getString("System_Title");
			    	 }
			    	 if(!array.getJSONObject(i).isNull("System_State")) {
			    		 JSONObject state1 =array.getJSONObject(i).getJSONObject("System_State");
			    		 if(!state1.isNull("name")) {
			    			 state = state1.getString("name");
//			    			 System.out.println(System_State);
			    		 }
			    	 }
			    	 if(!array.getJSONObject(i).isNull("System_CreatedDate")) {
			    		 Date date = null;
			    		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
			    		 SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			    		 creatDate = array.getJSONObject(i).getString("System_CreatedDate");
			    		 date = sdf.parse(creatDate);

			    	     Calendar calendar = Calendar.getInstance();
			    	     calendar.setTime(date);
			    	     calendar.set(Calendar.HOUR,calendar.get(Calendar.HOUR));
			    	     creatDate = simpleDateFormat.format(calendar.getTime());

			    		 
			    	 }
			    	 if(!array.getJSONObject(i).isNull("System_CreatedBy")) {
			    		 JSONObject name =array.getJSONObject(i).getJSONObject("System_CreatedBy");
			    		 if(!name.isNull("nameZh")) {
			    			 creator = name.getString("nameZh");
		    			
			    			 for(int j=0;j<array_tp.length()-1;j++) {
			    				JSONObject obj_tp = array_tp.getJSONObject(j);
//			    				System.out.println(obj_tp.getString("name"));
//			    				System.out.println(System_CreatedBy);
			    				if(obj_tp.getString("name").equals(creator)) {
			    					team = obj_tp.getString("team");
			    					number = obj_tp.getString("number");
//			    					System.out.println(team);
			    				}
			    			 }
			    		 }
			    	 }
			    	 
			    	 if(i==array.length()-1)
		    			 updateSql+= "('"+id+"','"+title+"','"+state+"','"+creator+"','"+number+"','"+team+"','"+type+"','"+creatDate+"')";
		    		 else
		    			 updateSql+= "('"+id+"','"+title+"','"+state+"','"+creator+"','"+number+"','"+team+"','"+type+"','"+creatDate+"'),";
			     }
				    updateSql+=" on duplicate key update id=values(id),title=values(title),state=values(state),creator=values(creator),"
				    		+ "number=values(number),team=values(team),type=values(type),creatDate=values(creatDate);";
//				    System.out.println(updateSql);
				    DaoCMCC dao = new DaoCMCC();
				    dao.execute(updateSql);
				    String typeSql1 = "update ichange_ec set type='1CI' where lower(title) like lower('%1CI%') and lower(title) not like '%iwork%';";
				    String typeSql2 = "update ichange_ec set type='2CI' where lower(title) like lower('%2CI%') and lower(title) not like '%iwork%';";
				    String typeSql3 = "update ichange_ec set type='3CIfeature' where lower(title) like lower('%3CI特性%') and lower(title) not like '%iwork%';";
				    String typeSql4 = "update ichange_ec set type='3CImaoyan' where lower(title) like lower('%3CI冒烟%') and lower(title) not like '%iwork%';";
				    String typeSql5 = "update ichange_ec set type='iwork' where lower(title) like '%iwork%';";
				    dao.execute(typeSql1);
				    dao.execute(typeSql2);
				    dao.execute(typeSql3);
				    dao.execute(typeSql4);
				    dao.execute(typeSql5);
					dao.close();
		     }
		     
		     
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}	
	
	public  void timerTask() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 30);
		calendar.set(Calendar.SECOND, 0);
		Date date = calendar.getTime();
		System.out.println(date);
	    new Timer().schedule(new TimerTask(){  
	        @Override  
	        public void run() {  
	        	myThread = new MyThread2();  
	            myThread.start();	
	        }},date,1000*24*60*60);  
	  } 
	  
	
	class MyThread2 extends Thread { 
		    public void run() {  
		    	intihttpclient();
		    	String url = "https://icosg.dt.zte.com.cn/ZXRDCloud/RDCloud/WIC/rest/workspaces/RAN/queries/query_work_items";
				String sql_tp = "select * from teampeople";
				DaoCMCC dao = new DaoCMCC();
				ResultSet rs = dao.executeQuery(sql_tp);
				JSONArray array_tp = new JSONArray();
				try {
					array_tp = ResultSetToJsonArray.resultSetToJsonArray(rs);
				} catch (JSONException | SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				dao.close();
			    QueryIchangeinfo( url,"1",array_tp);
				System.out.println("结束");
		    	
		    }
	}
}





