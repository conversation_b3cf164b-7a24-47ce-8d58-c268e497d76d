package utils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.security.auth.message.callback.PrivateKeyCallback.Request;

import org.apache.commons.collections.map.HashedMap;
import org.json.JSONArray;

import dao.DaoCMCC;

public class testpooltime2 {
	public static void main(String[] args) {
//		reqnum();
		DaoCMCC dao = new DaoCMCC();
		Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd");
	    String updatetime=sbf.format(d);
	    updatetime="2025-06-23";

        String Sql="SELECT poolname,sum(waittime),count(*) FROM iwork2.jobwaitinfo where ifnew = 1 and updatetime>'2025-06-16 00:00:00' and updatetime<'2025-06-23 00:00:00' group by poolname;";
        ResultSet rs = dao.executeQuery(Sql);

        try {
			while(rs.next()) {
				String temppoolname = rs.getString(1);
				int tempwaittime = rs.getInt(2);
				int tempalltask = rs.getInt(3);
//		        String sql1="update rdctestcase set ismatch='"+ismatch+"' where id='"+id+"';";
//		        dao.execute(sql1);
				System.out.println(temppoolname);
//				System.out.println(tempwaittime);
//				System.out.println(tempalltask);
			    String updateSql="insert into stapooltime(date,poolname,totalwaittime,totaljob,avgwaittime) values";
			    updateSql+= "('"+updatetime+"','"+temppoolname+"','"+tempwaittime+"','"+tempalltask+"','"+(double)tempwaittime/(double)tempalltask+"')";
			    updateSql+=" on duplicate key update date=values(date),poolname=values(poolname),totalwaittime=values(totalwaittime),totaljob=values(totaljob),avgwaittime=values(avgwaittime);";
			    dao.execute(updateSql);

			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

        dao.close();
	}
	
	
	public static void reqnum() {
		DaoCMCC dao = new DaoCMCC();
		Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd");
	    String updatetime=sbf.format(d);

        String Sql="SELECT SUBSTRING_INDEX(jobid, '-', 1) AS jobid,poolname FROM iwork2.jobwaitinfo where ifnew = 1 and updatetime>'2025-02-10 00:00:00' and updatetime<'2025-02-17 00:00:00' group by poolname;";
        ResultSet rs = dao.executeQuery(Sql);
        Map<String, Set<String>> map = new HashedMap();
        try {
			while(rs.next()) {
				String temppoolname = rs.getString(2);
				String tempjobid = rs.getString(1);
				String sql2 = "SELECT systemid FROM iwork2.jobtime where classify = '0' and id = '"+tempjobid+"' and starttime >'2025-02-10 00:00:00' and starttime<'2025-02-17 00:00:00';";
				ResultSet rs2 = dao.executeQuery(sql2);
				while(rs2.next()) {
					String tempString = rs2.getString(1);
					if(!tempString.equals("")) {
						if(map.containsKey(temppoolname)) {
							String[] tempStrings = tempString.split("/");
							Set<String> tempSet = map.get(temppoolname);

							for(int i=0;i<tempStrings.length;i++) {
								tempSet.add(tempStrings[i]);
							}
							map.put(temppoolname, tempSet);

						}else {
							Set<String> tempSet = new HashSet<String>();
							String[] tempStrings = tempString.split("/");
							for(int i=0;i<tempStrings.length;i++) {
								tempSet.add(tempStrings[i]);
							}
							map.put(temppoolname, tempSet);
						}
					}
				}
				//				System.out.println(temppoolname);
//			    String updateSql="insert into stapooltime(date,poolname,totalwaittime,totaljob,avgwaittime) values";
//			    updateSql+= "('"+updatetime+"','"+temppoolname+"','"+tempwaittime+"','"+tempalltask+"','"+(double)tempwaittime/(double)tempalltask+"')";
//			    updateSql+=" on duplicate key update date=values(date),poolname=values(poolname),totalwaittime=values(totalwaittime),totaljob=values(totaljob),avgwaittime=values(avgwaittime);";
//			    dao.execute(updateSql);

			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        for(Entry<String, Set<String>> e:map.entrySet()) {
        	System.out.println(e.getKey());
        	System.out.println(e.getValue());
        	for(String s:e.getValue()) {
        		
        	}
        }

        dao.close();
	}
}
