package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.json.JSONArray;
import org.json.JSONException;

import dao.DaoCMCC;


/**
 * Servlet implementation class QueryTotalInfoServlet
 */
@WebServlet("/QueryTotalInfoServlet")
public class QueryTotalInfoServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;   
	 private MyThread2 myThread; 
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryTotalInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	@Override
	public void init() throws ServletException {
		// TODO Auto-generated method stub
		//super.init();
		 System.out.println("开始");
		 this.timerTask();
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		secondCIautorateinfo("2024-03-23");
		JSONArray result = new JSONArray();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
	}
	


	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doGet(request, response);
	}
	
	private void secondCIautorateinfo(String date) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		//测试用例数
//		int allusecase=0;
//		String sql10="SELECT count(*) FROM rdctestcase where ptype=0  and executephase='FRT' and tag like '%1级CI%';";
//		ResultSet rs10 = dao.executeQuery(sql10);
//		String sql11="SELECT count(*) FROM rdctestcase where ptype=1  and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and executephase='FRT' and "
//				+ "( (version='V5.65.10' and TestCaseApplicableVersion like '%V5.65.20%' and  ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%')) ) or "
//				+ "(TestCaseApplicableVersion like '%V5.65.20%' and  version='V5.65.20')   );";
//		ResultSet rs11 = dao.executeQuery(sql11);
//		String sql12="SELECT count(*) FROM rdctestcase where ptype=2  and executephase='FRT' and tag like '%3级CI%' and version='V5.65.20';";
//		ResultSet rs12 = dao.executeQuery(sql12);
//		String sql13="SELECT count(*) FROM rdctestcase where ptype=3  and executephase='FRT' and tag like '%3级CI%';";
//		ResultSet rs13 = dao.executeQuery(sql13);
//		String sql14="SELECT count(*) FROM rdctestcase where ptype=1  and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and executephase='IOT' and"
//				+ " ( (version='V5.65.10' and TestCaseApplicableVersion like '%V5.65.20%' and ((teamdir='IOT团队' and tag like '%V5.65.10-IOT%' ))) or "
//				+ "(version='V5.65.20' and tag like '%V5.65.20-IOT%'));";
//		ResultSet rs14 = dao.executeQuery(sql14);

		int allusecase=0;
		String sql11="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ApplicableVersion like '%V5.85.10%'  and ptype=1  and  (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ";
		ResultSet rs11 = dao.executeQuery(sql11);
		String sql12 = "SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and ((ptype=2 and  executephase='FRT' and tag like '%3级CI%' and ApplicableVersion like '%V5.85.10%') or (ptype=3 and  executephase='FRT' and tag like '%3级CI%' and ApplicableVersion like '%V5.85.10%') or (ptype=0 and  executephase='FRT' and tag like '%1级CI%' and ApplicableVersion like '%V5.85.10%'));";
		ResultSet rs12 = dao.executeQuery(sql12);
		
		
		try {
//			if(rs10.next()) {
//				allusecase += rs10.getInt(1);
//			}
			if(rs11.next()) {
				allusecase += rs11.getInt(1);
			}
			if(rs12.next()) {
				allusecase += rs12.getInt(1);
			}
//			if(rs13.next()) {
//				allusecase += rs13.getInt(1);
//			}
//			if(rs14.next()) {
//				allusecase += rs14.getInt(1);
//			}
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//测试用例数
		int autousecase=0;

//		String sql0="SELECT count(*) FROM rdctestcase where ptype=0 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%1级CI%';";
//		ResultSet rs0 = dao.executeQuery(sql0);
//		String sql1="SELECT count(*) FROM rdctestcase where ptype=1 and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='4-待部署') and executephase='FRT' and "
//				+ "( (version='V5.65.10' and TestCaseApplicableVersion like '%V5.65.20%' and  ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%')) ) or "
//				+ "(TestCaseApplicableVersion like '%V5.65.20%' and  version='V5.65.20'  ) );";
//		ResultSet rs1 = dao.executeQuery(sql1);
//		String sql2="SELECT count(*) FROM rdctestcase where ptype=2 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%' and version='V5.65.20';";
//		ResultSet rs2 = dao.executeQuery(sql2);
//		String sql3="SELECT count(*) FROM rdctestcase where ptype=3 and (hasautomated='2-已自动化' or hasautomated='5-已部署') and executephase='FRT' and tag like '%3级CI%';";
//		ResultSet rs3 = dao.executeQuery(sql3);
//		String sql4="SELECT count(*) FROM rdctestcase where ptype=1 and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='4-待部署') and executephase='IOT' and "
//				+ " ( (version='V5.65.10' and TestCaseApplicableVersion like '%V5.65.20%' and ((teamdir='IOT团队' and tag like '%V5.65.10-IOT%' ))) or "
//				+ "(version='V5.65.20' and tag like '%V5.65.20-IOT%'));";
//		ResultSet rs4 = dao.executeQuery(sql4);
		String sql0="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ApplicableVersion like '%V5.85.10%' and hasautomated!='1-无法自动化' and ptype=1  and   (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ";
		ResultSet rs0 = dao.executeQuery(sql0);
		String sql1 = "SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and ((ptype=2 and  executephase='FRT' and tag like '%3级CI%' and ApplicableVersion like '%V5.85.10%') or (ptype=3 and  executephase='FRT' and tag like '%3级CI%' and ApplicableVersion like '%V5.85.10%') or (ptype=0 and  executephase='FRT' and tag like '%1级CI%' and ApplicableVersion like '%V5.85.10%'));";
		ResultSet rs1 = dao.executeQuery(sql1);
		
		try {
			if(rs0.next()) {
				autousecase += rs0.getInt(1);
			}
			if(rs1.next()) {
				autousecase += rs1.getInt(1);

			}
//			if(rs2.next()) {
//				autousecase += rs2.getInt(1);
//			}
//			if(rs3.next()) {
//				autousecase += rs3.getInt(1);
//			}
//			if(rs4.next()) {
//				autousecase += rs4.getInt(1);
//			}
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		System.out.println("all自动化用例数:"+autousecase);
		System.out.println(allusecase);

	    String updateSql="insert into autoratesta(date,total) values";
	    updateSql+= "('"+date+"','"+String.format("%.2f", Double.valueOf((double)autousecase/(double)allusecase*100))+"')";
	    updateSql+=" on duplicate key update date=values(date),total=values(total);";
	    dao.execute(updateSql);
//	    System.out.println(updateSql);
		dao.close();
	}


public  void timerTask() {  
        new Timer().schedule(new TimerTask(){  
            @Override  
            public void run() {  
            	myThread = new MyThread2();  
	            myThread.start();	
            }},new Date(),1000*60*10*3);  
 } 
  class MyThread2 extends Thread {
	    public void run() {
    		Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	    String updatetime=sbf.format(d);
    	    if(updatetime.split(" ")[1].split(":")[0].equals("00")) {
				secondCIautorateinfo(updatetime.split(" ")[0]);
			}
	    }	      
	}
}
