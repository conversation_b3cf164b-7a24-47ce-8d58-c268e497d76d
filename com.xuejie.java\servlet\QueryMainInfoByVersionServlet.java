package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;

@WebServlet("/QueryMainInfoByVersionServlet")
public class QueryMainInfoByVersionServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryMainInfoByVersionServlet() {
        super();
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doPost(request, response);
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("text/html");
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");

        // 鑾峰彇鐗堟湰鍙傛暟
        String version = request.getParameter("version");
        if (version == null || version.trim().isEmpty()) {
            version = "V5.85.10"; // 榛樿鐗堟湰
        }

        DaoCMCC dao = new DaoCMCC();
        JSONArray result = new JSONArray();

        try {
            // 鏌ヨ鐗规�х粍鏁版嵁
            JSONArray featureArray = getFeatureGroupData(dao, version);
            result.put(featureArray);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            dao.close();
        }

        PrintWriter out = response.getWriter();
        out.print(result);
        out.flush();
        out.close();
    }

    /**
     * 鏍规嵁鐗堟湰鑾峰彇鐗规�х粍鏁版嵁
     */
    private JSONArray getFeatureGroupData(DaoCMCC dao, String version) throws JSONException, SQLException {
        JSONArray array = new JSONArray();

        String sql19 = "SELECT distinct performancetopic FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and ApplicableVersion like '%" + version + "%' and ptype=1 and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'";
        ResultSet rs19 = dao.executeQuery(sql19);

        while (rs19.next()) {
            JSONObject object = new JSONObject();
            object.put("feature", rs19.getString(1));
            object.put("totalnum", 0);
            object.put("autonum", 0);
            object.put("autoratenum", 0);
            object.put("exeratenum", 0);
            object.put("exenum", 0);
            object.put("matchnum", 0);
            object.put("reportnum", 0);
            array.put(object);
        }

        // 鏌ヨ璇︾粏鏁版嵁
        String sql15 = "SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and ApplicableVersion like '%" + version + "%' and ptype=1 and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'";
        ResultSet rs15 = dao.executeQuery(sql15);
        JSONArray jsonArray15 = ResultSetToJsonArray.resultSetToJsonArray(rs15);

		for(int i=0;i<jsonArray15.length();i++) {
			JSONObject object = jsonArray15.getJSONObject(i);
			String tempfeature = object.getString("performancetopic");
			String tempexecutephase = object.getString("executephase");
			String temphasautomated = object.getString("hasautomated");
			String tempid = object.getString("id");
			String hasreport = object.getString("hasreport");
			String tempExecuteFrequency = object.getString("ExecuteFrequency");
			String tempismatch = object.getString("envismatch");
			for(int j=0;j<array.length();j++) {
				JSONObject obj = array.getJSONObject(j);
				if(obj.getString("feature").equals(tempfeature)) {
						if(temphasautomated.contains("已部署")) {
							obj.put("exeratenum", obj.getInt("exeratenum")+1) ;
							if(!tempExecuteFrequency.equals("")) {
								obj.put("exenum", obj.getInt("exenum")+Integer.valueOf(tempExecuteFrequency)) ;
							}
						}
					obj.put("totalnum", obj.getInt("totalnum")+1) ;
					if(!temphasautomated.equals("1-无法自动化")) {
						obj.put("autonum", obj.getInt("autonum")+1) ;
						if(tempismatch.equals("2")) {
							obj.put("matchnum", obj.getInt("matchnum")+1) ;
						}
					}
					if(hasreport.equals("1")) {
						obj.put("reportnum", obj.getInt("reportnum")+1) ;
					}
				}
			}
		}
        // 璁＄畻姣旂巼
        for (int i = 0; i < array.length(); i++) {
            int autonum = array.getJSONObject(i).getInt("autonum");
            int exeratenum = array.getJSONObject(i).getInt("exeratenum");
            int totalnum = array.getJSONObject(i).getInt("totalnum");
            int matchnum = array.getJSONObject(i).getInt("matchnum");
            int reportnum = array.getJSONObject(i).getInt("reportnum");

            // 纭繚鎶ュ憡鏁颁笉瓒呰繃鎵ц鏁�
            if (reportnum >= exeratenum) {
                array.getJSONObject(i).put("reportnum", exeratenum);
                reportnum = exeratenum;
            }

            // 璁＄畻鑷姩鍖栫巼
            if (autonum == 0 || totalnum == 0) {
                array.getJSONObject(i).put("autorate", 0);
            } else {
                double autorate = (double) autonum / (double) totalnum * 100;
                array.getJSONObject(i).put("autorate", Math.round(autorate * 100.0) / 100.0);
            }

            // 璁＄畻鎵ц鐜�
            if (autonum == 0 || exeratenum == 0) {
                array.getJSONObject(i).put("exerate", 0);
            } else {
                double exerate = (double) exeratenum / (double) autonum * 100;
                array.getJSONObject(i).put("exerate", Math.round(exerate * 100.0) / 100.0);
            }

            // 璁＄畻鍖归厤鐜�
            if (autonum == 0 || matchnum == 0) {
                array.getJSONObject(i).put("matchrate", 0);
            } else {
                double matchrate = (double) matchnum / (double) autonum * 100;
                array.getJSONObject(i).put("matchrate", Math.round(matchrate * 100.0) / 100.0);
            }

            // 璁＄畻鎶ュ憡鐢熸垚鐜�
            if (exeratenum == 0 || reportnum == 0) {
                array.getJSONObject(i).put("reportrate", 0);
            } else {
                double reportrate = (double) reportnum / (double) exeratenum * 100;
                array.getJSONObject(i).put("reportrate", Math.round(reportrate * 100.0) / 100.0);
            }
        }

        return array;
    }
}
