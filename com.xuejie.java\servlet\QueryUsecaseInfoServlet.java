package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseInfoServlet")
public class QueryUsecaseInfoServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		DaoCMCC dao = new DaoCMCC();
		
		String page = request.getParameter("pageIndex");
		String limit = request.getParameter("pageSize");
		String tempversion = request.getParameter("version");
		String tempversion2 = request.getParameter("version2");

		String version = "";
		String version2 = "";

		if(tempversion==null) {
			
		}else {
			version = tempversion;
		}
		if(tempversion2==null) {
			
		}else {
			version2 = tempversion2;
		}
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 

        JSONArray result = new JSONArray();
		JSONArray jsonArray0 = new JSONArray();
		JSONArray jsonArray1 = new JSONArray();
		JSONArray jsonArray2 = new JSONArray();
		JSONArray jsonArray3 = new JSONArray();

		String sql1="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ApplicableVersion like '%"+version+"%'  and ( (ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ) order by CreatedDate desc limit "+limit1+","+limit2+";";
		ResultSet rs1 = dao.executeQuery(sql1);
		String sql2="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=2 and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%' order by CreatedDate desc limit "+limit1+","+limit2+";";
		ResultSet rs2 = dao.executeQuery(sql2);
		String sql3="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=3 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%' limit "+limit1+","+limit2+";";
		ResultSet rs3 = dao.executeQuery(sql3);
		
		String sql8="select t.id from (SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as t join paraminfo as t2 on t.id=t2.id group by t2.id;";
		ResultSet rs8 = dao.executeQuery(sql8);
		Set<String> paramedSet = new HashSet<String>();
		
		String sql5="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=1 and ApplicableVersion like '%"+version+"%'  and  ((ApplicableVersion like '%V5.75.10%'  or ApplicableVersion like '%V5.75.20%' or ApplicableVersion like '%V5.85.10%') and (state='就绪' or state='新建') and (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' );";
		ResultSet rs5 = dao.executeQuery(sql5);
		String sql6="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=2 and ApplicableVersion like '%"+version2+"%' and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%';";
		ResultSet rs6 = dao.executeQuery(sql6);
		String sql7="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=3 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%3级CI%';";
		ResultSet rs7 = dao.executeQuery(sql7);

		int ptype0=0,ptype1=0,ptype2=0,ptype3=0,ptype4=0;
		try {
			while(rs8.next()) {
				paramedSet.add(rs8.getString(1));
			}
			jsonArray1 = ResultSetToJsonArray.resultSetToJsonArray(rs1);
			for(int i=0;i<jsonArray1.length();i++) {
				JSONObject tempJsonObject = jsonArray1.getJSONObject(i);
				if(paramedSet.contains(tempJsonObject.getString("id")) ) {
					tempJsonObject.put("parambushu", 1);
				}else {
					tempJsonObject.put("parambushu", 0);
				}
			}
			jsonArray2 = ResultSetToJsonArray.resultSetToJsonArray(rs2);
			for(int i=0;i<jsonArray2.length();i++) {
				JSONObject tempJsonObject = jsonArray2.getJSONObject(i);
				if(paramedSet.contains(tempJsonObject.getString("id")) ) {
					tempJsonObject.put("parambushu", 1);
				}else {
					tempJsonObject.put("parambushu", 0);
				}
			
			}
			jsonArray3 = ResultSetToJsonArray.resultSetToJsonArray(rs3);
			if(rs5.next()) {
				ptype1 = rs5.getInt(1);
			}
			if(rs6.next()) {
				ptype2 = rs6.getInt(1);

			}
			if(rs7.next()) {
				ptype3 = rs7.getInt(1);

			}

		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		for(int i=0;i<jsonArray1.length();i++) {
			JSONObject tempJsonObject = jsonArray1.getJSONObject(i);
			if(!tempJsonObject.isNull("ApplicableVersion")) {
				String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
				if(ApplicableVersion.contains("V5.75.10")) {
					tempJsonObject.put("version", "V5.75.10");
				}
				if(ApplicableVersion.contains("V5.75.20")) {
					tempJsonObject.put("version", "V5.75.20");
				}
				if(ApplicableVersion.contains("V5.85.10")) {
					tempJsonObject.put("version", "V5.85.10");
				}
			}
		}
		for(int i=0;i<jsonArray2.length();i++) {
			JSONObject tempJsonObject = jsonArray2.getJSONObject(i);
			if(!tempJsonObject.isNull("ApplicableVersion")) {
				String ApplicableVersion = tempJsonObject.getString("ApplicableVersion");
				if(ApplicableVersion.contains("V5.75.10")) {
					tempJsonObject.put("version", "V5.75.10");
				}
				if(ApplicableVersion.contains("V5.75.20")) {
					tempJsonObject.put("version", "V5.75.20");
				}
				if(ApplicableVersion.contains("V5.85.10")) {
					tempJsonObject.put("version", "V5.85.10");
				}
			}
		}
		JSONObject object = new JSONObject();
		object.put("code", 0).put("msg", "");
		object.put("ptype0", ptype0).put("ptype1", ptype1).put("ptype2", ptype2).put("ptype3",ptype3);
		object.put("data0", jsonArray0).put("data1", jsonArray1).put("data2", jsonArray2).put("data3",jsonArray3);

		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




