package util;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.json.JSONArray;
import org.json.JSONObject;

import com.mysql.cj.xdevapi.Schema.CreateCollectionOptions;

import dao.DaoCMCC;
//var data1 = [{
//    id: '1',
//    name: 'xxx',
//    createTime: '2019/11/18 10:44:00',
//    children: [ {
//        id: '1_1',
//        name: 'xxx',
//        createTime: '2019/11/18 10:44:00'
//    }]
//}, {
//    id: '2',
//    name: 'xxx',
//    createTime: '2019/11/18 10:44:00',
//    
//    children: [{
//        id: '2_1',
//        name: 'xxx',
//        
//        state: 0,
//        createTime: '2019/11/18 10:44:00',
//        children: [{
//            id: '2_1_1',
//            name: 'xxx',
//            state: 0,
//            createTime: '2019/11/18 10:44:00',
//        }
//        	
//        	
//        	
//        ]
//    }]
//}];
public class json {
	 public void create(String info) {
		 JSONArray result= new JSONArray();
		 DaoCMCC dao = new DaoCMCC();
		 String[] list = info.split("&");
		 for(int i=0;i<list.length;i++) {
			 String env = list[i].split("#")[0];
			 String tempcase = list[i].split("#")[1];
			 String[] cases = tempcase.split("=");
			 
			 JSONObject envObject = new JSONObject();
			 envObject.put("name", env);
			 envObject.put("level", 1);
			 envObject.put("children", new JSONArray());
			 for(int j=0;j<cases.length;j++) {
				 JSONObject useObject = new JSONObject();
				 useObject.put("level", 2);
				 useObject.put("name", cases[j]);
				 useObject.put("children", new JSONArray());

				 String sql = "SELECT * FROM iwork.paraminfo where id='"+cases[j]+"';";
				 ResultSet rs = dao.executeQuery(sql);
				 try {
					while(rs.next()) {
						 if(rs.getString(1)==null) {
							 continue;
						 }else {
							 JSONObject paramObject = new JSONObject();
							 paramObject.put("level", 3);
							 paramObject.put("name", rs.getString(5));
							 paramObject.put("mocname", rs.getString(3));
							 paramObject.put("paraname", rs.getString(4));
							 paramObject.put("defaultvalue", rs.getString(6));
							 useObject.getJSONArray("children").put(paramObject);
						 }
					 }
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				 envObject.getJSONArray("children").put(useObject);
			 }
			 result.put(envObject);
		 }
		 dao.close();
	 }	

	 
	 public static void main(String[] args) {
		String e ="310801-26#case1=81972=78824&310801-27#case1=case2=90500";
		
		json j = new json();
		j.create(e);
	}
}
