var xmlHttp=false;  
function createXMLHttpRequest() {
    if (window.ActiveXObject){
          xmlHttp = new  ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest){
          xmlHttp = new XMLHttpRequest();
     }  
}

function init(){
		createXMLHttpRequest();
    	xmlHttp.onreadystatechange = initcallBack;
    	var url="QueryMainInfoServlet";  
    	xmlHttp.open("GET", url, true);
    	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
    	xmlHttp.send(null);	
}

function initcallBack()
{
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data=xmlHttp.responseText;
			data = JSON.parse(data);
			info(data[0],data[10]);
			var c1 = document.getElementById("chart1");
			
            chart1(c1,data[1],data[2],data[3],data[4],data[5],data[6]);	
            var c2 = document.getElementById("chart2");
            chart2(c2,data[7],data[8],data[11]);	
			table2(data[9]);
			table3(data[12]);

		}
	}	
}

function info(data,dataIchange){
	document.getElementById("info1_1").innerText=data.sumactnum;		
	document.getElementById("info1_2").innerText="执行任务条数:"+data.countjob;	
	document.getElementById("info2_1").innerText=data.allusecase;
	
	document.getElementById("info3_1").innerText=(data.ptype0)+(data.ptype1)+(data.ptype2)+(data.ptype3);
	document.getElementById("info3_2").innerHTML="1级CI环境:"+data.ptype0+"&nbsp;&nbsp;&nbsp;2级CI环境:"+data.ptype1+"&nbsp;&nbsp;&nbsp;3级CI环境:"+(data.ptype2+data.ptype3);

//	document.getElementById("info4_1").innerHTML="<br>70.97%(99.59%)";
	document.getElementById("info4_1").innerHTML="<br>"+data.exerate+"";	
	
//	document.getElementById("info4_1").innerHTML="<br>"+data.actusecasenum+"("+data.frtactrate+")";		
	
//	document.getElementById("info4_2").innerText="测试成功率:"+data.success;	
//	document.getElementById("info4_2").innerHTML="1级CI用例:"+data.actusecasenum0+"&nbsp;&nbsp;&nbsp;2级CI用例:"+data.actusecasenum1+"&nbsp;&nbsp;&nbsp;3级CI用例:"+(data.actusecasenum2+data.actusecasenum3);
	
	document.getElementById("info1").innerText=dataIchange.ci1;
	document.getElementById("info2").innerText=dataIchange.ci2;
	document.getElementById("info3").innerText=dataIchange.ci3feature;
	document.getElementById("info4").innerText=dataIchange.ci3maoyan;
	document.getElementById("info5").innerText=dataIchange.iwork;
	document.getElementById("info6").innerText=dataIchange.ci1 + dataIchange.ci2 + dataIchange.ci3feature + dataIchange.ci3maoyan + dataIchange.iwork;
}


function chart1(divname,datatitle,dataall,data0,data1,data2,data3){
		Highcharts.chart(divname, {
		chart: {
			
		},
		credits:{
			enabled:false
		},
		plotOptions:{
			column:{
				size:'100%',
				dataLabels:{
					enabled:false,
					focusable:false
					//format:'<b>{point.name}:</b>{point.y}',
					//distance:-70
				}	
			},
			spline:{
				size:'100%',
				dataLabels:{
					enabled:false,
					focusable:false
					//format:'<b>{point.name}:</b>{point.y}',
					//distance:-70
				}	
			}
		},
		
		tooltip:{
			pointFormat:"{point.y}"
		},
		title: {
			text: ''
		},
	xAxis: [{
		categories:datatitle, 
		crosshair: true,
		labels: {
				style: {
						
				}
		}
}],
		yAxis: [{ // Primary yAxis
				labels: {
						format: '{value}',
						style: {
							fontSize: '13px'
						}
				},
				title: {
						text: '数量',
				},
		},
		{ // Primary yAxis
				labels: {
						format: '{value}',
						style: {
							fontSize: '13px'
						}
				},
				title: {
						text: '比率',
				},
				opposite: true
		}
		],
		legend:{
			enabled:true,
			itemStyle: { color: 'black' }
		},
		series: [{
				name:"用例平均等待时长",
				data: data0,
				type: 'column',
				color: Highcharts.getOptions().colors[0],
				dataLabels:{
					enabled:true,
					format:'{point.y}',
					//distance:-70
				},
			},
			{
				name:"用例执行通过率",
				data: data1,
				type: 'spline',
				color: Highcharts.getOptions().colors[1],
				dataLabels:{
					enabled:true,
					format:'{point.y}%',
					//distance:-70
				},
			},
			{
				name:"用例平均执行时长",
				data: data2,
				type: 'column',
				color: Highcharts.getOptions().colors[2],
				dataLabels:{
					enabled:true,
					format:'{point.y}',
					//distance:-70
				},
			},
			{
				name:"用例匹配率",
				data: data3,
				type: 'spline',
				color: Highcharts.getOptions().colors[5],
				focusable:false,
				dataLabels:{
					enabled:true,
					format:'{point.y}%',
					//distance:-70
				},

			}
			]
	});
	
	
}



function chart2(divname,data,data1,data3){
	var count = data3[0]-1;
	var count2 = data3[0];

	var countlen = data3[0]+data3[1]-1;
	var chart = Highcharts.chart(divname, {
		chart: {
				zoomType: 'xy'
		},
		title: {
				text: "整体自动化率变化趋势",
		},
		xAxis: [{
				categories: data,
//				crosshair: true,
				labels: {
				rotation:-30
				},
				plotBands:[{
          // 0~6点
			          from: 0, // 起始，有时可能需要动态生成，这需要根据产品而定
			          to: count+1, // 结束
			          color: '#ECF5FF',
			          label: {
			            text: 'V5.65.10',
			            style: {
			              color: 'black',
			            },
			          },
			        },
			        {
			          // 6~8点
			          from: count2, // 起始
			          to: countlen, // 结束
			          color: '#ECFFFF',
			          label: {
			            text: 'V5.65.20',
			            style: {
			              color: 'black',
			            },
			          },
			        },
			        {
			          // 6~8点
			          from: data3[1], // 起始
			          to: data3[2], // 结束
			          color: '#ECF5FF',
			          label: {
			            text: 'V5.75.10',
			            style: {
			              color: 'black',
			            },
			          },
			        },
			        {
			          from: data3[2], // 起始
			          to: data3[3], // 结束
			          color: '#ECFFFF',
			          label: {
			            text: 'V5.75.20',
			            style: {
			              color: 'black',
			            },
			          },
			        },
			        {
			          from: data3[3], // 起始
			          to: data3[4], // 结束
			          color: '#ECF5FF',
			          label: {
			            text: 'V5.85.10',
			            style: {
			              color: 'black',
			            },
			          },
			        },
			]
		}],
		yAxis: [{ // Primary yAxis
				labels: {
						format: '{value}',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				},
				title: {
						text: '自动化率',
						style: {
								color: Highcharts.getOptions().colors[0]
						}
				}
				
		}],
		tooltip: {
				shared: true
		},
		series: [ {
				name: '自动化率',
				type: 'spline',
				data: data1,
				marker: {
						enabled: false
				},
				dataLabels:{
					enabled:true,
					format:'{point.y}%',
					//distance:-70
				}	,
				dashStyle: 'shortdot',
				tooltip: {
						valueSuffix: '%'
				},
//				  zoneAxis: [{
//                      value: '2023-09-27',
//                      color: '#f7a35c',
//                      dashStyle: 'dot'
//                  },{
//                     color: '#90ed7d'
//                 },
//             ],
				color:Highcharts.getOptions().colors[0]
				
		}]
});
}

function table2(data){
	var allnum=0;
	var allautonum=0;
	var allexenum=0;
	var allExecuteFrequencynum=0;
	var allmatch=0;
	var allreportnum=0;

//	var allfrtiotnum=0;
//	var allhuiguinum=0;
//	var allhuiguiautonum=0;
//	var allhuiguiwaitautonum=0;
//	var allhuiguinotautonum=0;

	for(var i=0;i<data.length;i++){
		allnum+=Number(data[i].totalnum)
//		allfrtiotnum+=Number(data[i].frtiotnum)
		allautonum+=Number(data[i].autonum)
		allexenum+=Number(data[i].exeratenum)
		allExecuteFrequencynum+=Number(data[i].exenum)
		allmatch+=Number(data[i].matchnum)
		allreportnum+=Number(data[i].reportnum)

//		allhuiguinum+=Number(data[i].huiguinum)
//		allhuiguiautonum+=Number(data[i].huiguiautonum)
//		allhuiguiwaitautonum+=Number(data[i].huiguiwaitautonum)
//		allhuiguinotautonum+=Number(data[i].huiguinotautonum)
	}
	var t2 = document.getElementById("tbody2");
	s="";
	for(var i=0;i<data.length;i++){
//				s+="<tr bgcolor='#F0FBFA'>"
			s+="<tr bgcolor='#F1FAFA'>"
//		s+="<tr>"
		s+="<td style='text-align:center;font-size:13px;overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].feature+"</td>";
		if(data[i].autorate<60){
			s+="<td bgcolor='#FF9797' style='text-align:center;font-size:13px;color:white'>"+data[i].autorate+"%</td>";
		}else{
			s+="<td style='text-align:center;font-size:13px;'>"+data[i].autorate+"%</td>";
		}
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].matchrate+"%</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].exerate+"%</td>";

		s+="<td style='text-align:center;font-size:13px;'>"+data[i].totalnum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].autonum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].exeratenum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].exenum+"</td>";
		
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].reportnum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].reportrate+"%</td>";

		s+="</tr>"
	}

	s+="<tr bgcolor='#F1FAFA'>"
	s+="<td style='text-align:center;font-size:13px;'>"+"总计"+"</td>";
		var data1 = (allautonum)/(allnum)*100;
	data1 = data1.toFixed(2);
//	s+="<td style='text-align:center;font-size:13px;'>"+data1+"%</td>";
	if(data1<60){
		s+="<td bgcolor='#FF9797' style='text-align:center;font-size:13px;color:white'>"+data1+"%</td>";
	}else{
		s+="<td style='text-align:center;font-size:13px;'>"+data1+"%</td>";
	}
		var data3 = (allmatch)/(allautonum)*100;
	data3 = data3.toFixed(2);
	s+="<td style='text-align:center;font-size:13px;'>"+data3+"%</td>";
		var data2 = (allexenum)/(allautonum)*100;
	data2 = data2.toFixed(2);
	s+="<td style='text-align:center;font-size:13px;'>"+data2+"%</td>";
	s+="<td style='text-align:center;font-size:13px;'>"+allnum+"</td>";
	s+="<td style='text-align:center;font-size:13px;'>"+allautonum+"</td>";



	s+="<td style='text-align:center;font-size:13px;'>"+allexenum+"</td>";

	s+="<td style='text-align:center;font-size:13px;'>"+allExecuteFrequencynum+"</td>";
	s+="<td style='text-align:center;font-size:13px;'>"+allreportnum+"</td>";
	var data4 = (allreportnum)/(allexenum)*100;
	data4 = data4.toFixed(2);
	s+="<td style='text-align:center;font-size:13px;'>"+data4+"%</td>";

	s+="</tr>"
	t2.innerHTML=s;
}


function table3(data){
	var allnum=0;
	var allautonum=0;
	var allexenum=0;

	for(var i=0;i<data.length;i++){
		allnum+=Number(data[i].totalnum)
		allautonum+=Number(data[i].autonum)
		allexenum+=Number(data[i].exeratenum)
	}
	var t2 = document.getElementById("tbody3");
	s="";
	for(var i=0;i<data.length;i++){
//				s+="<tr bgcolor='#F0FBFA'>"
			s+="<tr bgcolor='#F1FAFA'>"
//		s+="<tr>"
		s+="<td style='text-align:center;font-size:13px;overflow:hidden;word-break:keep-all;white-space:nowrap;'>"+data[i].team+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].totalnum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].autonum+"</td>";
//		s+="<td style='text-align:center;font-size:13px;'>"+data[i].autorate+"</td>";
		if(data[i].autorate<60){
			s+="<td bgcolor='#FF9797' style='text-align:center;font-size:13px;color:white'>"+data[i].autorate+"%</td>";
		}else{
			s+="<td style='text-align:center;font-size:13px;'>"+data[i].autorate+"%</td>";
		}
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].exeratenum+"</td>";
		s+="<td style='text-align:center;font-size:13px;'>"+data[i].exerate+"%</td>";
		s+="</tr>"
	}

	s+="<tr bgcolor='#F1FAFA'>"
	s+="<td style='text-align:center;font-size:13px;'>"+"总计"+"</td>";
	s+="<td style='text-align:center;font-size:13px;'>"+allnum+"</td>";
	s+="<td style='text-align:center;font-size:13px;'>"+allautonum+"</td>";

	var data1 = (allautonum)/(allnum)*100;
	data1 = data1.toFixed(2);
	if(data1<60){
		s+="<td bgcolor='#FF9797' style='text-align:center;font-size:13px;color:white'>"+data1+"%</td>";
	}else{
		s+="<td style='text-align:center;font-size:13px;'>"+data1+"%</td>";
	}
	s+="<td style='text-align:center;font-size:13px;'>"+allexenum+"</td>";
	var data2 = (allexenum)/(allautonum)*100;
	data2 = data2.toFixed(2);
//	if(data1<60){
//		s+="<td bgcolor='#FF9797' style='text-align:center;font-size:13px;color:white'>"+data1+"%</td>";
//	}else{
		s+="<td style='text-align:center;font-size:13px;'>"+data2+"%</td>";
//	}
	s+="</tr>"
	t2.innerHTML=s;
}

function queryinfo(){
	var layer=layui.layer
   	layer.open({
   			type: 2,
   			//area: [w+'px', h +'px'],testReload
   			area: ['80%', '80%'],		
   			fix: false, //不固定
   			maxmin: true,
   			shadeClose: true,
   			shade:0.4,
   			title: '执行率分析',
   			content: 'usecaseanalyse.jsp',
//   			end:function(){
//   				table.reload('testReload2');
//   				console.log("ok");
//   			}
   		});
}

//function initcallBack()
//{
//	if (xmlHttp.readyState == 4) {
//		if (xmlHttp.status == 200) {
//			var data=xmlHttp.responseText;
//			data = JSON.parse(data);
//			info(data[0],data[10]);
//			var c1 = document.getElementById("chart1");
//            chart1(c1,data[1],data[2],data[3],data[4],data[5],data[6]);	
//            var c2 = document.getElementById("chart2");
//            chart2(c2,data[7],data[8]);	
//			table2(data[9]);
//		}
//	}	
//}
//
 function addtk4() {
			var layer=layui.layer
    		layer.open({
    			type: 2,
    			//area: [w+'px', h +'px'],testReload
    			area: ['100%', '100%'],		
    			fix: false, //不固定
    			maxmin: true,
    			shadeClose: false,
    			shade:0.4,
    			title: '创建任务',
    			content: 'createpooljob.jsp',
    			end:function(){
    				init();    				
    			}
    		});
		
    }
    
function addtk2(){
	window.open("https://wxiot.zte.com.cn:9098/iWork2Use3/index.html");

}

// 版本切换函数
function changeVersion(){
	var selectedVersion = document.getElementById("versionSelect").value;
	// 更新表格标题
	document.getElementById("tableTitle").innerText = selectedVersion + "特性组用例";

	// 调用后台servlet获取新版本的数据
	createXMLHttpRequest();
	xmlHttp.onreadystatechange = changeVersionCallback;
	var url = "QueryMainInfoByVersionServlet?version=" + encodeURIComponent(selectedVersion);
	xmlHttp.open("GET", url, true);
	xmlHttp.setRequestHeader("Content-Type","multipart/form-data");
	xmlHttp.send(null);
}

// 版本切换回调函数
function changeVersionCallback(){
	if (xmlHttp.readyState == 4) {
		if (xmlHttp.status == 200) {
			var data = xmlHttp.responseText;
			data = JSON.parse(data);
			// 只更新特性组用例表格
			table2(data[0]);
		}
	}
}

