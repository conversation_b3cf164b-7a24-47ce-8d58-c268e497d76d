package servlet4;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;

import dao.DaoCMCC;
import util.UmeExe;

/**
 * Servlet implementation class QueryGnbInfoServlet
 */
@WebServlet("/QueryGnbInfoServlet")
public class QueryGnbInfoServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
	 private  HttpClient customHttpClient=null;   

       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryGnbInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		// TODO Auto-generated method stub
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		JSONArray result = new JSONArray();
		String jobid = request.getParameter("jobid");
		
		DaoCMCC dao = new DaoCMCC();

		String gnbids = "";
		String sql2 = "SELECT gnbid FROM jobdetails where jobid = '"+jobid+"'";
		ResultSet rs2 = dao.executeQuery(sql2);
		try {
			if(rs2.next()) {
				gnbids = rs2.getString(1);
			}
		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		String[] gnbinfos = gnbids.split("/");
		Set<String> gnbset = new HashSet<String>();
		for(int i=0;i<gnbinfos.length;i++) {
			if(!gnbinfos[i].equals("")) {
				gnbset.add(gnbinfos[i]);
			}
		}
		JSONArray cellresult = new JSONArray();
		JSONObject gnbidresult = new JSONObject();
		UmeExe umeExe = new UmeExe();
		for(String gnbid:gnbset) {
			System.out.println(gnbid);
			String codeString = "";
			String versionString = "";
			JSONArray array = umeExe.querygnbidstatus(gnbid);
			if(array.length()>=1) {
				versionString = array.getString(1);
				String code = array.getString(0);
				if(code.equals("1")) {
					codeString = "正常";
				}else {
					codeString = "断链";
				}
			}			
			String ume="";
			String sql = "SELECT gnbid,subnetwork,umeip FROM gnbinfo where gnbid = '"+gnbid+"';";
			String subnetwork = "";
			ResultSet rs = dao.executeQuery(sql);
			try {
				while(rs.next()) {
					subnetwork = rs.getString(2);
					ume=rs.getString(3);
				}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		    String nelist = "SubNetwork="+subnetwork +",ManagedElement="+gnbid;
		    intihttpclient();
			// API: 锟斤拷取Token
			 String gettokenURL = "https://"+ume+":28001/api/oauth2/v1/usercred/access_token";
			// API: 注锟斤拷Token
			 String distokenURL = "https://"+ume+":28001/api/oauth2/v1/logout";
			//锟斤拷取token锟斤拷锟斤拷body锟斤拷息
			
			JSONObject jsonObject = new JSONObject();
			if(ume.equals("***********")) {
				jsonObject.put("username","admin").put("password", "Zenap_123").put("grant_type", "PASSWORD");
	        }else if(ume.equals("*************")) {
				jsonObject.put("username","admin").put("password", "Zenap_123!@#").put("grant_type", "PASSWORD");
	        }else {
				jsonObject.put("username","adminiwork521").put("password", "Frt_2022@)@@").put("grant_type", "PASSWORD");
	        }
//			jsonObject.put("username","admin").put("password", "Frt_2022@)@@").put("grant_type", "PASSWORD");
		    
		    String token=logintoken(jsonObject,gettokenURL);
		    System.out.println(token);
		   
		    // API: 查询小区״̬
		    String necellinfourl="https://"+ume+":28001/api/currentarea/v1/modataquery";
		    cellresult =  QueryCellinfo(token,necellinfourl,nelist);
		    String fddltenecellinfourl="https://"+ume+":28001/api/currentarea/v1/modataquery";
		    cellresult = Query4gfddCellinfo(cellresult,token, fddltenecellinfourl, nelist);
		    String Tddltenecellinfourl="https://"+ume+":28001/api/currentarea/v1/modataquery";
		    cellresult = Query4gtddCellinfo(cellresult,token, Tddltenecellinfourl, nelist);
			logouttoken(token,distokenURL);
			
			JSONObject infoObject = new JSONObject();
			infoObject.put("code", codeString);
			infoObject.put("data", cellresult);
			infoObject.put("gnbid", gnbid);
			infoObject.put("version", versionString);

			result.put(infoObject);
		}
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
		
	}

	public JSONObject gnbisonline(String gnbid) {
		String url = "https://iwork.zx.zte.com.cn/iWork2UmeHelper/UmeGnbidIsOnlineServlet?gnbid="+gnbid;
		// TODO Auto-generated method stub
//		System.out.println(directory.toString()+":"+String.valueOf(actnum+1)+":"+url);
		JSONObject result = new JSONObject();
		intihttpclient();
	    Unirest.setHttpClient(customHttpClient);
        HttpResponse<JsonNode> httpresponse;
		try {
			httpresponse = Unirest.get(url)
					.header("content-type", "application/json")
			        .asJson();
			result = httpresponse.getBody().getObject();
		} catch (UnirestException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} 
		return result;
	}


	private JSONArray QueryCellinfo(String token, String url, String nelist) {
		// TODO Auto-generated method stub
	    JSONArray celllist=new JSONArray();

		try {
			
			JSONObject obj=new JSONObject("{\r\n"
					+ "    \"ManagedElementType\": \"ITBBU\",\r\n"
					+ "    \"neList\": [\""+nelist+"\"],\r\n"
					+ "    \"mocList\": [\"CellDefiningSSB\",\"NRCellDU\"],\r\n"
					+ "    \"attrFilter\": [\r\n"
					+ "        {\r\n"
					+ "            \"moc\": \"CellDefiningSSB\",\r\n"
					+ "            \"attrNames\": [\"pci\",\"ssbFrequency\"]\r\n"
					+ "        },\r\n"
					+ "         {\r\n"
					+ "            \"moc\": \"NRCellDU\",\r\n"
					+ "            \"attrNames\": [\"moId\",\"serviceStatus\",\"refNRPhysicalCellDU\",\"cellEsState\",\"adminState\",\"cellLocalId\"]\r\n"
					+ "        }\r\n"
					+ "    ]\r\n"
					+ "}");
//			System.out.println(obj.toString());
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	        	    .body(obj)
	                .asJson();  
		    JSONArray array=httpresponse.getBody().getObject().getJSONArray("result");
		    Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		    String updatetime=sbf.format(d);
		    for(int i=0;i<array.length();i++)
		    {
		    	String  gnbid=array.getJSONObject(i).getString("ne");      gnbid=gnbid.split("=")[2];
		    	JSONArray cellarray=array.getJSONObject(i).getJSONArray("moData");
		    	for(int j=0;j<cellarray.length();j++)
		    	{
		    		JSONObject cellobj=new JSONObject();
		    		//if(cellarray.getJSONObject(j).isNull("pci")) break;
		    		if(!cellarray.getJSONObject(j).isNull("pci"))
		    		{
		    			String pci=cellarray.getJSONObject(j).getString("pci");
		    			String ssbFrequency=cellarray.getJSONObject(j).getString("ssbFrequency");	
		    			String NRPhysicalCellDU=cellarray.getJSONObject(j).getString("ldn").split(",")[1]; NRPhysicalCellDU=NRPhysicalCellDU.split("=")[1];
		    			cellobj.put("pci",pci);cellobj.put("ssbFrequency",ssbFrequency);cellobj.put("NRPhysicalCellDU",NRPhysicalCellDU);cellobj.put("gnbid",gnbid);	
		    			cellobj.put("system","NR");	

		    			celllist.put(cellobj);
		    		}
		    	}
		    	for(int j=0;j<cellarray.length();j++)
		    	{
		    		if(!cellarray.getJSONObject(j).isNull("serviceStatus"))
		    		{
		    			String serviceStatus=cellarray.getJSONObject(j).getString("serviceStatus");
		    			String cellEsState = cellarray.getJSONObject(j).getString("cellEsState");
		    			String adminState = cellarray.getJSONObject(j).getString("adminState");
		    			String cellLocalId = cellarray.getJSONObject(j).getString("cellLocalId");

		    			String NRPhysicalCellDU=cellarray.getJSONObject(j).getString("refNRPhysicalCellDU").split(",")[1]; NRPhysicalCellDU=NRPhysicalCellDU.split("=")[1];
		    			for(int m=0;m<celllist.length();m++)
		    			{
		    				if(NRPhysicalCellDU.equals(celllist.getJSONObject(m).getString("NRPhysicalCellDU"))&&gnbid.equals(celllist.getJSONObject(m).getString("gnbid")))
		    				{
		    					if(serviceStatus.equals("OutOfService")) {
			    					celllist.getJSONObject(m).put("serviceStatus", "退服");
		    					}else if(serviceStatus.equals("InService")) {
		    						celllist.getJSONObject(m).put("serviceStatus", "在服");
		    					}
		    					
		    					celllist.getJSONObject(m).put("cellEsState", cellEsState);
		    					celllist.getJSONObject(m).put("cellLocalId", cellLocalId);

		    					if(adminState.equals("Locked")) {
		    						celllist.getJSONObject(m).put("adminState", "闭塞");
		    					}else if(adminState.equals("Shutdown")){
		    						celllist.getJSONObject(m).put("adminState", "正在闭塞");
		    					}else if(adminState.equals("Unlocked")){
		    						celllist.getJSONObject(m).put("adminState", "解闭塞");

		    					}
		    					
		    					break;
		    				}
		    			}
		    		}
		    	}
		    }
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return celllist;
	}
	
	//POST锟斤拷锟斤拷锟斤拷锟斤拷询锟斤拷锟斤拷NR小锟斤拷锟斤拷息
	protected JSONArray Query4gfddCellinfo(JSONArray celllist,String token,String url, String nelist) {		
		try {
			JSONObject obj=new JSONObject("{\r\n"
					+ "    \"ManagedElementType\": \"ITBBU\",\r\n"
					+ "    \"mocList\": [\"CUEUtranCellFDDLTE\"],\r\n"
					+ "    \"neList\": [\""+nelist+"\"],\r\n"

					+ "    \"attrFilter\": [\r\n"
					+ "         {\r\n"
					+ "            \"moc\": \"CUEUtranCellFDDLTE\",\r\n"
					+ "            \"attrNames\": [\"moId\",\"cellLocalId\",\"pci\",\"operState\",\"earfcnDl\",\"refPlmn\"]\r\n"
					+ "        }\r\n"
					+ "    ]\r\n"
					+ "}");
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	        	    .body(obj)
	                .asJson();  
		    JSONArray array=httpresponse.getBody().getObject().getJSONArray("result");
		    Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		    String updatetime=sbf.format(d);
		    for(int i=0;i<array.length();i++)
		    {
		    	String  gnbid=array.getJSONObject(i).getString("ne");      gnbid=gnbid.split("=")[2];
		    	JSONArray cellarray=array.getJSONObject(i).getJSONArray("moData");
		    	for(int j=0;j<cellarray.length();j++)
		    	{
		    		JSONObject cellobj=new JSONObject();
		    		if(!cellarray.getJSONObject(j).isNull("pci"))
		    		{
		    			String pci=cellarray.getJSONObject(j).getString("pci");
		    			String cellLocalId=cellarray.getJSONObject(j).getString("cellLocalId");
		    			String serviceStatus=cellarray.getJSONObject(j).getString("operState");	

		    			String ssbFrequency=cellarray.getJSONObject(j).getString("earfcnDl");	

		    			cellobj.put("pci",pci);
		    			cellobj.put("ssbFrequency",ssbFrequency);
		    			cellobj.put("gnbid",gnbid);
		    			if(serviceStatus.equals("1")) {
			    			cellobj.put("serviceStatus","在服");	
    					}else if(serviceStatus.equals("0")) {
			    			cellobj.put("serviceStatus","退服");	
    					}
//		    			cellobj.put("serviceStatus",serviceStatus);
		    			cellobj.put("cellLocalId",cellLocalId);	
		    			cellobj.put("system","LTEFDD");	

		    			celllist.put(cellobj);
		    		}
		    	}		    	
		    }
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return celllist;
	}
	
	//POST锟斤拷锟斤拷锟斤拷锟斤拷询锟斤拷锟斤拷NR小锟斤拷锟斤拷息
	protected JSONArray Query4gtddCellinfo(JSONArray celllist,String token,String url, String nelist) {		
		try {
			JSONObject obj=new JSONObject("{\r\n"
					+ "    \"ManagedElementType\": \"ITBBU\",\r\n"
					+ "    \"mocList\": [\"CUEUtranCellTDDLTE\"],\r\n"
					+ "    \"neList\": [\""+nelist+"\"],\r\n"
					+ "    \"attrFilter\": [\r\n"
					+ "         {\r\n"
					+ "            \"moc\": \"CUEUtranCellTDDLTE\",\r\n"
					+ "            \"attrNames\": [\"moId\",\"cellLocalId\",\"pci\",\"operState\",\"earfcn\",\"refPlmn\"]\r\n"
					+ "        }\r\n"
					+ "    ]\r\n"
					+ "}");
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	        	    .body(obj)
	                .asJson();  
		    JSONArray array=httpresponse.getBody().getObject().getJSONArray("result");
		    Date d = new Date(); SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		    String updatetime=sbf.format(d);
		    for(int i=0;i<array.length();i++)
		    {
		    	String  gnbid=array.getJSONObject(i).getString("ne");      gnbid=gnbid.split("=")[2];
		    	JSONArray cellarray=array.getJSONObject(i).getJSONArray("moData");
		    	for(int j=0;j<cellarray.length();j++)
		    	{
		    		JSONObject cellobj=new JSONObject();
		    		if(!cellarray.getJSONObject(j).isNull("pci"))
		    		{
		    			String pci=cellarray.getJSONObject(j).getString("pci");
		    			String cellLocalId=cellarray.getJSONObject(j).getString("cellLocalId");
		    			String serviceStatus=cellarray.getJSONObject(j).getString("operState");	

		    			String ssbFrequency=cellarray.getJSONObject(j).getString("earfcn");	

		    			
		    			if(!cellarray.getJSONObject(j).isNull("refPlmn")) {
			    			cellobj.put("refPlmn",cellarray.getJSONObject(j).getString("refPlmn"));	

		    			}
		    			cellobj.put("pci",pci);
		    			cellobj.put("ssbFrequency",ssbFrequency);
		    			cellobj.put("gnbid",gnbid);
//		    			cellobj.put("serviceStatus",serviceStatus);
		    			if(serviceStatus.equals("1")) {
			    			cellobj.put("serviceStatus","在服");	
    					}else if(serviceStatus.equals("0")) {
			    			cellobj.put("serviceStatus","退服");	
    					}
		    			cellobj.put("cellLocalId",cellLocalId);	
		    			cellobj.put("system","LTETDD");	

		    			celllist.put(cellobj);
		    		}
		    	}		    	
		    }
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return celllist;
	}	


	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doGet(request, response);
	}
	
	
	
	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
	

}
