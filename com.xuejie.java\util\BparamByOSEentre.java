package util;

import java.io.File;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.xml.bind.util.JAXBSource;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;

public class BparamByOSEentre {
	 private HttpClient customHttpClient=null;   

	public static void main(String[] args) {
		DaoCMCC dao = new  DaoCMCC();
//  	 String sql4="SELECT * FROM jobdetails where jobid='"+60+"' and ischecked=1 order by usecaseid+0;";
		String sql4="SELECT * FROM jobparavalue where remark=1 and  taskid ='"+1000+"' and caseremark='"+1+"' ;";

  	 ResultSet rs4 = dao.executeQuery(sql4);
  	 JSONArray jsonArray = new JSONArray();
	try {
		jsonArray = ResultSetToJsonArray.resultSetToJsonArray(rs4);
	} catch (JSONException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	} catch (SQLException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
	System.out.println(jsonArray);
	dao.close();
	BparamByOSEentre bparamByOSEentre = new BparamByOSEentre();
	bparamByOSEentre.execute(jsonArray,"0");
	
		
//		String path ="D:\\software\\eclipse\\project\\.metadata\\.plugins\\org.eclipse.wst.server.core\\tmp1\\wtpwebapps\\iWork2Use\\upload\\ITBBU_16016_911558_all_20230629161138.cpu" ;
//		File file = new File(path);
//		
//		BparamByOSE b = new BparamByOSE(file, "************", "429", null);
//		ExecutorService pool2 = Executors.newFixedThreadPool(5);
//		Future<JSONArray> future = pool2.submit(b);
	}
	
	public void execute(JSONArray paramArrayB, String flag) {
		DaoCMCC dao = new DaoCMCC();
		for(int i=0;i<paramArrayB.length();i++) {
			JSONObject object = paramArrayB.getJSONObject(i);
			String taskid = object.getString("taskid");
			String caseremark = object.getString("caseremark");
			String defaultvalue=object.getString("defaultvalue");
			String suggestvalue=object.getString("suggestvalue");
			String suggestvalue2=object.getString("suggestvalue2");
			String sql = "SELECT * FROM iwork2.jobdetails where jobid='"+taskid+"' and usecaseid='"+caseremark+"';";
			ResultSet rs = dao.executeQuery(sql);
			String gnbid="";
			String pci="";
			String usecaseid="";

			try {
				if(rs.next()) {
					gnbid = rs.getString(3);
					pci = rs.getString(4);
					usecaseid = rs.getString(7);
				}
				String sql3="SELECT umeip FROM gnbinfo where gnbid='"+gnbid+"';";
				ResultSet rs3 = dao.executeQuery(sql3);
				String umeip="";
				if(rs3.next()) {
					umeip = rs3.getString(1);
				}
				String nameofpath = "";
				if(flag.equals("0")) {
					String sql2 = "SELECT nameofpath FROM iwork2.paraminfo where id='"+usecaseid+"' and nameofb='"+defaultvalue+"';";
					ResultSet rs2 =dao.executeQuery(sql2);
					if(rs2.next()) {
						nameofpath = rs2.getString(1);
					}
				}else if(flag.equals("1")) {
					String sql2 = "SELECT nameofpath FROM iwork2.paraminfo where id='"+usecaseid+"' and nameofb='"+suggestvalue+"';";
					ResultSet rs2 =dao.executeQuery(sql2);
					if(rs2.next()) {
						nameofpath = rs2.getString(1);
					}
				}else if(flag.equals("2")) {
					String sql2 = "SELECT nameofpath FROM iwork2.paraminfo where id='"+usecaseid+"' and nameofb='"+suggestvalue2+"';";
					ResultSet rs2 =dao.executeQuery(sql2);
					if(rs2.next()) {
						nameofpath = rs2.getString(1);
					}

				}
				
				
				String url = "https://"+umeip+":28001/api/rantopo/v1/ranmes?nbiId="+gnbid;
				JSONArray gnbinfo = querygnbidinfo(url,umeip,gnbid);
				BparamByOSE b = new BparamByOSE(new File(nameofpath), umeip, gnbinfo.getString(7),gnbinfo);
				ExecutorService pool2 = Executors.newFixedThreadPool(5);
				Future<JSONArray> future = pool2.submit(b);
				
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
	}

	private JSONArray querygnbidinfo(String url,String umeip, String gnbid) {
		// TODO Auto-generated method stub
		JSONArray result = new JSONArray();
		intihttpclient();
		// API: ��ȡToken
		 String gettokenURL = "https://"+umeip+":28001/api/oauth2/v1/usercred/access_token";
		// API: ע��Token
		 String distokenURL = "https://"+umeip+":28001/api/oauth2/v1/logout";
		//��ȡtoken����body��Ϣ
		JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray=new JSONArray();
        if(umeip.equals("***********")) {
			jsonObject.put("username","admin").put("password", "Zenap_123").put("grant_type", "PASSWORD");
        }else if(umeip.equals("*************")) {
			jsonObject.put("username","admin").put("password", "Zenap_123!@#").put("grant_type", "PASSWORD");
        }
        else {
			jsonObject.put("username","admin").put("password", "Frt_2022@)@@").put("grant_type", "PASSWORD");
        }
	    String token=logintoken(jsonObject,gettokenURL);
	    
        Unirest.setHttpClient(customHttpClient);
        HttpResponse<JsonNode> httpresponse;
		try {
			httpresponse = Unirest.get(url)
					.header("content-type", "application/json")
				    .header("accept", "application/json")
				    .header("Z-ACCESS-TOKEN",token)
			        .asJson();
		     JSONArray array=httpresponse.getBody().getArray();
		     for(int i=0;i<array.length();i++)
		     {
		    	 
		    	String id=array.getJSONObject(i).getString("id");
		    	String subnetwork=array.getJSONObject(i).getString("subnetwork_nbiId");
		    	String subType=array.getJSONObject(i).getString("subType");
		    	String name=array.getJSONObject(i).getString("name");
		    	String displayName=array.getJSONObject(i).getString("displayName");
		    	String ip=array.getJSONObject(i).getString("ipAddress");
		    	result.put(gnbid).put(id).put(subnetwork).put(subType).put(name).put(displayName).put(ip);
		     }
		} catch (UnirestException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 


		String appid=queryappid(umeip,"ne_Res_import",token);  
		result.put(appid);
		logouttoken(token,distokenURL);

		
		return result;
	}
	
	protected void intihttpclient() { //��ʼ��http�ͻ��ˣ��ر�SSL��Ȩ
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //��ȡtockenʧ��
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
	
	private String queryappid(String umeip, String appname,String token) {
		// TODO Auto-generated method stub

		String name="";
		try {
			String url ="https://"+umeip+":28001/api/ofd-access/v1/ose/packets";
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
	        JSONArray array = httpresponse.getBody().getArray();
	        for(int i=0;i<array.length();i++) {
	        	JSONObject object = array.getJSONObject(i);
	        	if(!object.isNull("name")) {
	        		String temp = object.getString("name");
	        		if(temp.equals(appname)) {
	        			name = String.valueOf(object.getInt("id")) ;
	        		}
	        	}
	        }

	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return name; 
	}
	
	private String queryall(String token, String umeip) {
		String name="";
		try {
			String url ="https://"+umeip+":28001/api/ofd-access/v1/ose/packets";
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
	        JSONArray array = httpresponse.getBody().getArray();
	        for(int i=0;i<array.length();i++) {
	        	JSONObject object = array.getJSONObject(i);
	        	if(!object.isNull("displayName")) {
	        		String temp = object.getString("displayName");
	        		if(temp.equals("BaselineLab")) {
	        			name = String.valueOf(object.getInt("id")) ;
	        		}
	        	}
	        }

	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
		return name; 
	}
	

}
