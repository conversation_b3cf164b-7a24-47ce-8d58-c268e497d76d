package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.map.HashedMap;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseInfoServletby1CI")
public class QueryUsecaseInfoServletby1CI extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseInfoServletby1CI() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		DaoCMCC dao = new DaoCMCC();
		
		String page = request.getParameter("pageIndex");
		String limit = request.getParameter("pageSize");
		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		
		JSONArray jsonArray0 = new JSONArray();
		//创建任务
		String sql0="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%1级CI%' order by CreatedDate desc limit "+limit1+","+limit2+";";
		ResultSet rs0 = dao.executeQuery(sql0);
		String sql4="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and ptype=0 and (hasautomated !='1-无法自动化') and executephase='FRT' and tag like '%1级CI%';";
		ResultSet rs4 = dao.executeQuery(sql4);
		//分数以及是否通过
		String sql1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore ;";
		ResultSet rs1 = dao.executeQuery(sql1);
		Map<String, String> ifpassMap = new HashedMap();
		String sql1_1 = "SELECT concat(devName,'_',TestVersion) as devandversion,ifpass FROM iwork2.kpiscore2 ;";
		ResultSet rs1_1 = dao.executeQuery(sql1_1);
		Map<String, String> ifpassMap2 = new HashedMap();
		//版本
		String sql2 = "SELECT gnbid,version FROM iwork2.gnbinfo;";
		ResultSet rs2 = dao.executeQuery(sql2);
		Map<String, String> versionMap = new HashedMap();
		int ptype0=0;
		try {
			
			
			jsonArray0 = ResultSetToJsonArray.resultSetToJsonArray(rs0);
			if(rs4.next()) {
				ptype0 = rs4.getInt(1);
			}
			while(rs2.next()) {
				String tempgnbid = rs2.getString(1);
				String tempversion = rs2.getString(2);
				versionMap.put(tempgnbid, tempversion);
			}
			
			while(rs1.next()) {
				String tempgnbid = rs1.getString(1);
				String tempversion = rs1.getString(2);
				ifpassMap.put(tempgnbid, tempversion);
			}
			
			while(rs1_1.next()) {
				String tempgnbid = rs1_1.getString(1);
				String tempversion = rs1_1.getString(2);
				ifpassMap2.put(tempgnbid, tempversion);
			}
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		for(int i=0;i<jsonArray0.length();i++) {
			JSONObject tempObject = jsonArray0.getJSONObject(i);
			if(!tempObject.isNull("title")) {
				String temptitle = tempObject.getString("title");
				String tempgnbid = temptitle.split("-")[0];
				if(versionMap.containsKey(tempgnbid)) {
					tempObject.put("gnbversion", versionMap.get(tempgnbid));
				}else {
					tempObject.put("gnbversion", "");
				}
				String tempdevandver = temptitle.split("-")[temptitle.split("-").length-1];
				if(ifpassMap.containsKey(tempdevandver)) {
					tempObject.put("ifpass", ifpassMap.get(tempdevandver));
				}else {
					tempObject.put("ifpass", "");
				}
				if(ifpassMap2.containsKey(tempdevandver)) {
					tempObject.put("ifpass2", ifpassMap2.get(tempdevandver));
				}else {
					tempObject.put("ifpass2", "");
				}
			}
		}
		JSONObject object = new JSONObject();
		object.put("code", 0).put("msg", "");
		object.put("ptype0", ptype0);
		object.put("data0", jsonArray0);

		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




