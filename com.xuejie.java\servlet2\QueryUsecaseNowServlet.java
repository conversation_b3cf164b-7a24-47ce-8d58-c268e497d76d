package servlet2;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.map.HashedMap;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import utils.DealPaths;

/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryUsecaseNowServlet")
public class QueryUsecaseNowServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryUsecaseNowServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");

		String ptype = request.getParameter("ptype");
		String rdccfg = request.getParameter("dirid");

		JSONObject object = new JSONObject();
		if(!rdccfg.equals("")) {
			String url = "http://10.57.84.83:8080/XJRdcPanel/QueryUsecaseByDiridServlet2?dirid="+rdccfg+"&ptype="+ptype+"";
//			System.out.println(url);
			object =  rdcusecase(url);

		}


		
		
		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	
	
	protected JSONObject rdcusecase(String url) {
		JSONObject resultArray = new JSONObject();
		try {	    
			intihttpclient();;
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	                .asJson(); 
	        resultArray = httpresponse.getBody().getObject();
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
		return resultArray;
	}
	
	protected void intihttpclient() { 
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
}




