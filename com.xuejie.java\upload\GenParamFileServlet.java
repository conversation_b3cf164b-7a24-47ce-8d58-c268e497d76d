//package upload;
//import java.io.File;
//import java.io.IOException;
//import java.io.PrintWriter;
//import java.util.HashMap;
//import java.util.Map;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
//import javax.net.ssl.SSLContext;
//import javax.security.cert.X509Certificate;
//import javax.servlet.ServletException;
//import javax.servlet.annotation.WebServlet;
//import javax.servlet.http.HttpServlet;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//import org.apache.http.client.HttpClient;
//import org.apache.http.conn.ssl.NoopHostnameVerifier;
//import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
//import org.apache.http.impl.client.HttpClients;
//import org.apache.http.ssl.SSLContextBuilder;
//import org.json.JSONArray;
//import org.json.JSONObject;
//
//import com.mashape.unirest.http.HttpResponse;
//import com.mashape.unirest.http.Unirest;
//import com.mashape.unirest.http.exceptions.UnirestException;
//
//import dao.DaoCMCC;
//
///**
// * Servlet implementation class QueryHWInfoServlet
// */
//@WebServlet("/GenParamFileServlet")
//public class GenParamFileServlet extends HttpServlet {
//	 private static final long serialVersionUID = 1L;
//	 private HttpClient customHttpClient=null;  
//    /**
//     * @see HttpServlet#HttpServlet()
//     */
//    public GenParamFileServlet() {
//        super();
//        // TODO Auto-generated constructor stub
//    }
//    
//	/**
//	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
//	 */
//	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
//		// TODO Auto-generated method stub
//		doPost(request,response);
//	}
//
//	/**
//	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
//	 */
//	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
//		response.setContentType("text/html");
//		request.setCharacterEncoding("UTF-8");  
//		response.setCharacterEncoding("UTF-8");
//		
//		String paramString= request.getParameter("paracfg1");
//		String allusecase= request.getParameter("allusecase");
//
//		
//		DaoCMCC dao = new DaoCMCC();
//		
//		String allparampath = "";
//		String recommandparampath = "";
//		String threepath = "";
//
//		String sql="select nameofpath from paramfile where nameoffile='"+allparamString+"'";
//		System.out.println(sql);
//		ResultSet rs = dao.executeQuery(sql);
//		try {
//			if(rs.next()) {
//				allparampath = rs.getString(1);
//			}
//		} catch (SQLException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
//		String sql1="select nameofpath from paramfile where nameoffile='"+recommandparamString+"'";
//		System.out.println(sql1);
//		ResultSet rs1 = dao.executeQuery(sql1);
//		try {
//			if(rs1.next()) {
//				recommandparampath = rs1.getString(1);
//			}
//		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		String sql11="select nameofpath from paramfile where nameoffile='"+threeString+"'";
//		System.out.println(sql11);
//		ResultSet rs11 = dao.executeQuery(sql11);
//		try {
//			if(rs11.next()) {
//				threepath = rs11.getString(1);
//			}
//		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		
//		dao.close();
//		
//		JSONObject array = scenejudge(allparampath,threepath);
//		System.out.println(array);
//		JSONObject result = paramjudge(recommandparampath, allparampath, array);
//		System.out.println(result);
//		JSONObject result1 = sta(result);
//		JSONArray tempArray = new JSONArray();
//		tempArray.put(result1);
//		System.out.println(result1);
//		
//		PrintWriter out = response.getWriter(); 
//		JSONArray resultArray = new JSONArray();
//		
//		JSONObject abJsonObject = new JSONObject();
//		abJsonObject.put("abnormal_parameters", result.getJSONArray("abnormal_parameters").toString());
//		JSONArray abJsonArray= new JSONArray();
//		abJsonArray.put(abJsonObject);
//		resultArray.put(result.getJSONArray("details")).put(tempArray).put(abJsonArray);
//				
//		out.print(resultArray);
//		out.flush();
//		out.close();
//	}
//
//	
//	protected void intihttpclient() { 
//		try {
//	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
//	            public boolean isTrusted(X509Certificate[] chain, String authType) {
//	                return true;
//	            }
//	        }).build();
//	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
//	        Unirest.setHttpClient(customHttpClient);
//	    } catch (Exception e) {
//	    	e.printStackTrace();
//	    }
//	}
//	
//	public JSONObject scenejudge(String allparampath,String threepath) {
//		String url = "http://***********:8010/paramcheck/paramcheck";
//		System.out.println(url);
//		JSONObject result = new JSONObject();
//		intihttpclient();
//	    Unirest.setHttpClient(customHttpClient);
//        HttpResponse<String> httpresponse;
//        
//        File allparamfile = new File(allparampath);
//        File threefile = new File(threepath);
//
//		Map<String, Object> map=new HashMap<String, Object>();
//		map.put("allparamfile",allparamfile);
//		map.put("threefile",threefile);
//
//		try {
//			httpresponse = Unirest.post(url)
////					.header("content-type", "application/json")
//	        	    .fields(map)
//			        .asString();
//			String teString = httpresponse.getBody().toString();
//			if(teString.contains("Error")) {
//				
//			}else {
//				result = new JSONObject(teString);
//			}
//			
//		} catch (UnirestException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		} 
//		return result;
//	}
//	
//	public JSONObject paramjudge(String recommandparampath,String allparampath,JSONObject array) {
//		String url = "http://***********:8004/ume/compare-parameters3/";
//		System.out.println(url);
//		JSONObject result = new JSONObject();
//		intihttpclient();
//	    Unirest.setHttpClient(customHttpClient);
//        HttpResponse<String> httpresponse;
//        
//        File recommandfile = new File(recommandparampath);
//        File allfile = new File(allparampath);
//
//		Map<String, Object> map=new HashMap<String, Object>();
//		map.put("json_data_str",array.toString());
//		map.put("script01",recommandfile);
//		map.put("script02",allfile);
//
//		try {
//			httpresponse = Unirest.post(url)
////					.header("content-type", "application/json")
//	        	    .fields(map)
//			        .asString();
//			String teString = httpresponse.getBody().toString();
////			System.out.println(teString);
//			if(teString.contains("Error")) {
//				
//			}else {
//				JSONObject tempJsonObject = new JSONObject(teString);
////				if(!tempJsonObject.isNull("results")) {
////					result = tempJsonObject.getJSONArray("results");
////				}
//				result = tempJsonObject;
//			}
//			
//		} catch (UnirestException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		} 
//		return result;
//	}
//	
//	public JSONObject sta(JSONObject array) {
//		String url = "http://***********:8004/umecheckoutput";
//		System.out.println(url);
//		JSONObject result = new JSONObject();
//		intihttpclient();
//	    Unirest.setHttpClient(customHttpClient);
//        HttpResponse<String> httpresponse;
//		Map<String, Object> map=new HashMap<String, Object>();
//		map.put("body",array);
//
//		try {
//			httpresponse = Unirest.post(url)
//					.header("content-type", "application/json")
//					.body(array)
//			        .asString();
//			String teString = httpresponse.getBody().toString();
//			if(teString.contains("Error")) {
//				
//			}else {
//				JSONObject tempJsonObject = new JSONObject(teString);
//				result = tempJsonObject;
//			}
//			
//		} catch (UnirestException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		} 
//		return result;
//	}
//}
//
//
//
//
