package servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryConfigUsecaseInfoServlet")
public class QueryConfigUsecaseInfoServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryConfigUsecaseInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		String id = request.getParameter("id");
		
		DaoCMCC dao = new DaoCMCC();
		
		
        JSONArray result = new JSONArray();
        id = "ZXPFM-"+id;
        String systemid = id;
        
        
		String sqString = "select id from rdctestcase where systemid = '"+id+"'";
		ResultSet rSet1 = dao.executeQuery(sqString);
		try {
			if(rSet1.next()) {
				id = rSet1.getString(1);
			}
		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
//		String sql="SELECT * FROM paraminfo where id='"+id+"' and remark=0;";
		String sql1="SELECT * FROM paraminfo where id='"+id+"' and (remark='1_1' or remark='1_2') ;";
		String sql3="SELECT * FROM paraminfo where id='"+id+"' and (remark='2_1' or remark='2_2' or remark='2_3');";
		String sql2="SELECT uemodel,uemodelcustom,element FROM rdctestcase where id='"+id+"';";
		
		String sql4="SELECT remark,nameofb,delayy,durationn FROM usecasespainfo where id='"+id+"';";
		
		String sql5="SELECT nameofb FROM paraminfo where id='"+id+"' and (remark='3');";

		String sql6="SELECT suggestvalue,nameofb,nameofpath FROM paraminfo where id='"+id+"' and (remark='4');";
		
		
		String sql7="SELECT flag,remark,delayy,durationn FROM usecasessaloginfo where systemid='"+systemid+"';";

		String element = "";
		JSONArray jsonArray1 = new JSONArray();
		JSONArray jsonArray2 = new JSONArray();
		String remark = "0";
		String spafile = "";
		String spadelay = "3";
		String spadurationn = "1";
		String realkpifile = "";
		
		String isuds = "0";
		String udschoose = "0";
		String udscmd = "";

		//创建任务
		ResultSet rs1 = dao.executeQuery(sql1);
		ResultSet rs2 = dao.executeQuery(sql2);
		ResultSet rs3 = dao.executeQuery(sql3);
		ResultSet rs4 = dao.executeQuery(sql4);
		ResultSet rs5 = dao.executeQuery(sql5);
		ResultSet rs6 = dao.executeQuery(sql6);
		ResultSet rs7 = dao.executeQuery(sql7);

		JSONArray jsonArray7 = new JSONArray();

		try {
			jsonArray1 = ResultSetToJsonArray.resultSetToJsonArray(rs1);
			jsonArray2 = ResultSetToJsonArray.resultSetToJsonArray(rs3);
			jsonArray7 = ResultSetToJsonArray.resultSetToJsonArray(rs7);
			if(jsonArray7.length()<2) {
				Date d = new Date();
			    SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			    String updatetime=sbf.format(d);

		        //更改终端界限
			     String updateSql="insert into usecasessaloginfo(systemid,flag,updatetime) values";
				 updateSql+= "('"+systemid+"','0','"+updatetime+"')";
			     updateSql+=" on duplicate key update systemid=values(systemid),flag=values(flag),updatetime=values(updatetime);";
		        dao.execute(updateSql);
		        
		        updateSql="insert into usecasessaloginfo(systemid,flag,updatetime) values";
				 updateSql+= "('"+systemid+"','1','"+updatetime+"')";
			     updateSql+=" on duplicate key update systemid=values(systemid),flag=values(flag),updatetime=values(updatetime);";
		        dao.execute(updateSql);
		        sql7="SELECT flag,remark,delayy,durationn FROM usecasessaloginfo where systemid='"+systemid+"';";
				rs7 = dao.executeQuery(sql7);
		        jsonArray7 = ResultSetToJsonArray.resultSetToJsonArray(rs7);
			}

			if(rs2.next()) {
				if(rs2.getString(3)!=null) {
					element = rs2.getString(3);
				}
			}
			if(rs4.next()) {
				if(rs4.getString(1)!=null) {
					remark = rs4.getString(1);
				}
				if(rs4.getString(2)!=null) {
					spafile = rs4.getString(2);
				}
				if(rs4.getString(3)!=null) {
					spadelay = rs4.getString(3);
				}
				if(rs4.getString(4)!=null) {
					spadurationn = rs4.getString(4);
				}
			}
			
			if(rs5.next()) {
				if(rs5.getString(1)!=null) {
					realkpifile = rs5.getString(1);
				}
			}
			
			if(rs6.next()) {
				if(rs6.getString(1)!=null) {
					isuds = rs6.getString(1);
				}
				if(rs6.getString(2)!=null) {
					udschoose = rs6.getString(2);
				}
				if(rs6.getString(3)!=null) {
					udscmd = rs6.getString(3);
				}
			}
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		JSONArray aaaaArray = new JSONArray();
		aaaaArray.put("").put("").put("").put("").put("").put("").put("").put("")
		.put("").put("").put("").put("").put("")
		.put("").put("").put("").put("").put("").put("").put("")  
		.put("").put("").put("").put("")
		;
		if(!element.equals("")) {
			JSONObject object = new JSONObject(element);
	        for (String key : object.keySet()) {
	            JSONArray array = object.getJSONArray(key);
//	            System.out.println(key + ": " + array);
	            //NR交付场景
	            if(key.equals("FoaTestRequestSource")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(0,array.getString(0));
	            	}
	            }
	            //LTE交付场景
	            if(key.equals("AauVersion")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(1,array.getString(0));
	            	}
	            }
	            //需求适配运营商频段
	            if(key.equals("sj_ABCLOSE")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(2,array.getString(0));
	            	}
	            }
	            //环境组网
	            if(key.equals("ANDRIOD_VERSION")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(3,array.getString(0));
	            	}
	            }
	            //用户数
	            if(key.equals("sj_APExample")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(4,array.getString(0));
	            	}
	            }
	            //干扰等级
	            if(key.equals("AcceptanceTestCaseState")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(5,array.getString(0));
	            	}
	            }
	            //干扰类型
	            if(key.equals("sj_ABSOLVE")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(6,array.getString(0));
	            	}
	            }
	            //网络时延
	            if(key.equals("AdsState")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(7,array.getString(0));
	            	}
	            }
	            //用户业务分布及动态变
	            if(key.equals("AisgType")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(8,array.getString(0));
	            	}
	            }
	            //终端类型
	            if(key.equals("AiAssistedActivity")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(9,array.getString(0));
	            	}
	            }
	            //上行PRB利用率
	            if(key.equals("AsilLevel")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(10,array.getString(0));
	            	}
	            }
	            //下行PRB利用率
	            if(key.equals("ItemCodeOfBugCenter")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(11,array.getString(0));
	            	}
	            }
	            
	            //商用组网
	            if(key.equals("CSCid")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(12,array.getString(0));
	            	}
	            }
	            
	            //参数套
	            if(key.equals("CiLevel")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(13,array.getString(0));
	            	}
	            }
	            
	            //业务类型
	            if(key.equals("FeatureDescriptionName")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(14,array.getString(0));
	            	}
	            }
	            
	            //3CI大综合
	            if(key.equals("TestCaseSource")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(15,array.getString(0));
	            	}
	            }
	            
	            //参数生效影响
	            if(key.equals("AndroidPlatform")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(16,array.getString(0));
	            	}
	            }
	            
	            //是否需要特殊环境
	            if(key.equals("AchievementOfCbbReusingRate")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(17,array.getString(0));
	            	}
	            }
	            
	          //干扰方向
	            if(key.equals("FnrDiffAnalysis")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(18,array.getString(0));
	            	}
	            }
	            
	          //干扰特征
	            if(key.equals("sj_FOTA_TEST_FROM")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(19,array.getString(0));
	            	}
	            }
	            //上行CCE资源利用率
	            if(key.equals("ZXPFM_ULCCEResUti")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(20,array.getString(0));
	            	}
	            }
	            //下行CCE资源利用率	
	            if(key.equals("ZXPFM_DlCCEResUti")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(21,array.getString(0));
	            	}
	            }
	            //上行CCE失败比例
	            if(key.equals("ZXPFM_UlCCEFailRatio")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(22,array.getString(0));
	            	}
	            }
	            //下行CCE失败比例
	            if(key.equals("ZXPFM_DlCCEFailRatio")) {
	            	if(array.get(0)!=JSONObject.NULL) {
	            		aaaaArray.put(23,array.getString(0));
	            	}
	            }
	        }
		}
		
		JSONArray array4 = new JSONArray();
		array4.put(remark).put(spafile).put(spadelay).put(spadurationn);
//		System.out.println(aaaaArray);
//        result.put(jsonArray);
        result.put(jsonArray1);
        result.put(jsonArray2);
//        result.put(finaluemodel);
//        result.put(finaluemodels);
        result.put(aaaaArray);
        result.put(array4);
        result.put(realkpifile);
		JSONArray array5 = new JSONArray();
		array5.put(isuds).put(udschoose).put(udscmd);
        result.put(array5);
        result.put(jsonArray7);

//        System.out.println(array4);
		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




