package utils;

import java.sql.ResultSet;
import java.sql.SQLException;

import dao.DaoCMCC;

public class ToolList {
	
	
	public String judgeisbackup( String id,String usecaseid) {
		// TODO Auto-generated method stub
		DaoCMCC dao = new DaoCMCC();
		String backupfilename="";
		String sql = "SELECT backupfilename FROM iwork2.jobdetailsbackup where jobid='"+id+"' and usecaseid='"+usecaseid+"';";
		ResultSet rs = dao.executeQuery(sql);
		try {
			if(rs.next()) {
				if(rs.getString(1)==null) {
					
				}else {
					backupfilename = rs.getString(1);
				}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return backupfilename;
	}

}
