package util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class changetime {
	
	public static final String  FORMAT_UTC ="yyyy-MM-dd'T'HH:mm:ss'Z'";
	
	public static String change(Date date) {
		SimpleDateFormat dataDateFormat = new SimpleDateFormat(FORMAT_UTC);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.HOUR,-8);
		return dataDateFormat.format(calendar.getTime());

	}
	public static void main(String[] args) {
		Date date = new Date();
	    SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    System.out.println(sbf.format(date));
	    System.out.println(change(date));
	}
}
