package servlet;


import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Random;
import java.util.Set;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryMainInfoServlet")
public class QueryMainInfoServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryMainInfoServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		DaoCMCC dao = new DaoCMCC();
		
        JSONArray result = new JSONArray();

		//创建任务
//		String sql0="SELECT count(*),sum(actnum*usecasenum) FROM job;";
        String sql0 = "SELECT count(*),sum(difftime) FROM iwork2.jobtime where classify ='0';";
		ResultSet rs0 = dao.executeQuery(sql0);
		int countjob=0;
		int sumactnum=0;
		try {
			if(rs0.next()) {
				countjob=rs0.getInt(1);
				sumactnum = rs0.getInt(2);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//测试用例数
		int allusecase=0;
//		int frtallusecase= 0;
		int allautousecase=0;
		
//		String sql11="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and  TestCaseApplicableVersion like '%V5.65.20%' and version='V5.65.10' and ptype=1 and (((hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='4-待部署') and executephase='IOT' and ((teamdir='IOT团队' and tag like '%V5.65.10-IOT%' )))  or (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他') and (hasautomated ='2-已自动化' or hasautomated ='5-已部署' or hasautomated ='4-待部署') and executephase='FRT' and ((teamdir='中移FOA' and (tag like '%中移FOA测试用例-V5.65.10%' and tag like '%中移FOA需求用例%')) or (teamdir!='中移FOA' and tag like '%5.65.10守护需求%')));";
		String sql11="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and  ApplicableVersion like '%V5.85.10%'  and (state='就绪' or state='新建') and ptype=1   and   (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'  ";
//		and ( performancetopic!='MU' and performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他' and performancetopic!='非特性组用例')
		ResultSet rs11 = dao.executeQuery(sql11);
		String sql12 = "SELECT  id,hasautomated FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ((ptype=2 and  executephase='FRT' and tag like '%3级CI%' and ApplicableVersion like '%V5.85.10%') or (ptype=3 and  executephase='FRT' and tag like '%3级CI%') or (ptype=0 and  executephase='FRT' and tag like '%1级CI%' and ApplicableVersion like '%V5.85.10%'));";
		ResultSet rs12 = dao.executeQuery(sql12);
//		String sql13="SELECT count(*) FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and  ApplicableVersion like '%V5.75.10%' and (state='就绪' or state='新建') and ptype=1 and ( performancetopic!='MU' and performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他' and performancetopic!='非特性组用例' and performancetopic!='TNR基础性能' and performancetopic!='语音性能' and performancetopic!='高铁' and performancetopic!='KPI') and  (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'";
//		ResultSet rs13 = dao.executeQuery(sql13);
	
		String sql14="SELECT id FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and hasautomated!='1-无法自动化' and  ApplicableVersion like '%V5.85.10%'  and (state='就绪' or state='新建') and ptype=1  and   (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' ";
		ResultSet rs14 = dao.executeQuery(sql14);
//		and ( performancetopic!='MU' and performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他' and performancetopic!='非特性组用例')
		JSONArray jsonArray12 = new JSONArray();
		JSONArray jsonArray14 = new JSONArray();
		try {
			jsonArray14 = ResultSetToJsonArray.resultSetToJsonArray(rs14);
			jsonArray12 = ResultSetToJsonArray.resultSetToJsonArray(rs12);

		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		try {
			if(rs11.next()) {
				allusecase += rs11.getInt(1);
			}
//			if(rs13.next()) {
//				frtallusecase =rs13.getInt(1);
//			}
//			if(rs12.next()) {
//				int tempcount= rs12.getInt(1);
				allusecase += jsonArray12.length();
//				frtallusecase +=jsonArray12.length();
				allautousecase+=jsonArray12.length();
//			}
			allautousecase +=jsonArray14.length();
			
//			System.out.println(allusecase);
//			System.out.println(allautousecase);
		} catch (JSONException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		//环境数
		int ptype0=0,ptype1=0,ptype2=0,ptype3=0;
		String sql2="SELECT ptype,count(*) FROM cienvinfo where ptype=1 or ptype=2 or ptype=3 group by ptype;";
		ResultSet rs2 = dao.executeQuery(sql2);
		try {
			while(rs2.next()) {
				String temp = rs2.getString(1);
				if(temp.equals("1")) {
					ptype1 = rs2.getInt(2);
				}else if(temp.equals("2")) {
					ptype2 = rs2.getInt(2);
				}else if(temp.equals("3")) {
					ptype3 = rs2.getInt(2);
				}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String sql3="select count(*) from (select * from (SELECT a.gnbid,a.pci,b.ip,a.devname,a.celllocalid FROM necellinfo as a,gnbinfo as b  where a.gnbid=b.gnbid and a.devname is not null and a.devname !=\"\") as c join cienvinfo as d on c.ip=d.neip and d.ptype=0) as t";
		ResultSet rs3 = dao.executeQuery(sql3);
		try {
			if(rs3.next()) {
				ptype0 = rs3.getInt(1);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		//执行用例
		int actusecasenum=0;
		//成功用例
		
		HashSet<String> exeusecase = new HashSet<String>();
		int actusecasenum0=0,actusecasenum1=0,actusecasenum2=0,actusecasenum3=0;
//		String sql5="SELECT distinct usecaseid,ptype FROM jobtestinfo where ptype!=2;";
//		ResultSet rs5 = dao.executeQuery(sql5);
//		try {
//			while(rs5.next()) {
//				String tempptype = rs5.getString(2);
//				String tempusecaseid = rs5.getString(1);
//				exeusecase.add(tempusecaseid);
//				if(tempptype.equals("0")) {
//					actusecasenum0 = actusecasenum0+1;
//				}else if(tempptype.equals("1")){
//					actusecasenum1 = actusecasenum1+1;
//				}else if(tempptype.equals("3")){
//					actusecasenum3 = actusecasenum3+1;
//				}
//			}
//		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		System.out.println(actusecasenum0);
//		System.out.println(actusecasenum3);
//		HashMap<String, Set<String>> map = new HashMap<String, Set<String>>();
//		String sql5_2="SELECT distinct usecaseid,ptype,team FROM jobtestinfo where ptype=2;";
//		ResultSet rs5_2 = dao.executeQuery(sql5_2);
//		try {
//			while(rs5_2.next()) {
//				String tempteam ="";
//				if(rs5_2.getString(3)==null) {
//					
//				}else {
//					tempteam=rs5_2.getString(3);
//				}
//				String[] tempusecases = rs5_2.getString(1).split("/");
//				
//				for(int j=0;j<tempusecases.length;j++) {
//					exeusecase.add(tempusecases[j]);
//
//					if(map.containsKey(tempteam)) {
//						Set<String> tempSet = map.get(tempteam);
//						tempSet.add(tempusecases[j]);
//						map.put(tempteam, tempSet );
//					}else {
//						Set<String> tempSet = new HashSet<String>();
//						tempSet.add(tempusecases[j]);
//						map.put(tempteam, tempSet );
//					}
//				}
//			}
//		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		for(Entry<String, Set<String>> s:map.entrySet()) {
//			actusecasenum2=actusecasenum2+s.getValue().size();
//		}
//		actusecasenum = actusecasenum0+actusecasenum1+actusecasenum2+actusecasenum3;
//		System.out.println(actusecasenum);
		//单独FRT;
//		int frtactusecasenum1=0;
//		String sql5_3="SELECT count(distinct usecaseid) FROM jobtestinfo where ptype=1 and stage='FRT';";
//		ResultSet rs5_3 = dao.executeQuery(sql5_3);
//		try {
//			while(rs5_3.next()) {
//				frtactusecasenum1 = rs5_3.getInt(1);
//			}
//		} catch (SQLException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//
//		int frtactusecase = 0;
//		frtactusecase = actusecasenum0 +frtactusecasenum1+actusecasenum2+actusecasenum3;
		
		
//		JSONArray jsonArray_title=new JSONArray();
//		JSONArray jsonArray=new JSONArray();
//		String sql6_title="SELECT DATE_FORMAT(testtime,'%Y%-%m%-%d') days,count(usecasename) as count,ptype FROM jobtestinfo  where DATE_SUB(CURDATE(), INTERVAL 6 DAY) <= date(testtime)  group by days,ptype";
//		ResultSet rssql6_title = dao.executeQuery(sql6_title);
//		JSONArray temoJsonArray = new JSONArray(); 
//		JSONArray jsonArray_0 = new JSONArray();
//		JSONArray jsonArray_1 = new JSONArray();
//		JSONArray jsonArray_2 = new JSONArray();
//		JSONArray jsonArray_3 = new JSONArray();
//		Set<String> titleSet = new LinkedHashSet<String>();
//		try {
//			temoJsonArray = ResultSetToJsonArray.resultSetToJsonArray(rssql6_title);
////			while (rssql6_title.next()) {
////				jsonArray_title.put(rssql6_title.getString(1));
////				jsonArray.put(rssql6_title.getInt(2));
////			}
//		} catch (SQLException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
//		for(int i=0;i<temoJsonArray.length();i++) {
//			titleSet.add(temoJsonArray.getJSONObject(i).getString("days"));
//		}
//		for(String s:titleSet) {
//			jsonArray_0.put(0);jsonArray_1.put(0);jsonArray_2.put(0);jsonArray_3.put(0);jsonArray.put(0);
//			jsonArray_title.put(s);
//		}
//		for(int i=0;i<temoJsonArray.length();i++) {
//			JSONObject jsonObject = temoJsonArray.getJSONObject(i);
//			String tempdays = jsonObject.getString("days");
//			String tempptype = jsonObject.getString("ptype");
//			int tempcount = jsonObject.getInt("count");
//			
//			for(int j=0;j<jsonArray_title.length();j++) {
//				if(jsonArray_title.getString(j).equals(tempdays)) {
//					if(tempptype.equals("0")) {
//						jsonArray_0.put(j,tempcount);
//					}else if(tempptype.equals("1")) {
//						jsonArray_1.put(j,tempcount);
//					}else if(tempptype.equals("2")) {
//						jsonArray_2.put(j,tempcount);
//					}else {
//						jsonArray_3.put(j,tempcount);
//					}
//					jsonArray.put(j,jsonArray.getInt(j)+  tempcount);
//				}
//			}
//		}
		String sql6="SELECT * FROM iwork2.staall order by date asc;";
		ResultSet rssql6 = dao.executeQuery(sql6);
		
		JSONArray jsonArray_title=new JSONArray();
		JSONArray jsonArray_0 = new JSONArray();
		JSONArray jsonArray_1 = new JSONArray();
		JSONArray jsonArray_2 = new JSONArray();
		JSONArray jsonArray_3 = new JSONArray();
		try {
			while(rssql6.next()) {
				jsonArray_title.put(rssql6.getString(1));
				jsonArray_0.put(Double.valueOf(rssql6.getString(2)) );
				jsonArray_1.put(Double.valueOf(rssql6.getString(3)));
				jsonArray_2.put(Double.valueOf(rssql6.getString(4)));
				jsonArray_3.put(Double.valueOf(rssql6.getString(5)));

			}
		} catch (SQLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		

		JSONArray jsonArray_title2=new JSONArray();
		JSONArray jsonArray2=new JSONArray();
		String sql7="SELECT date,total FROM autoratesta;";
		ResultSet rs7= dao.executeQuery(sql7);
		try {
			while(rs7.next()) {
				jsonArray_title2.put(rs7.getString(1));
//				JSONObject object = new JSONObject();
//				object.put("y", rs7.getDouble(2)).put("color", "red");
				jsonArray2.put(rs7.getDouble(2));
			}
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		JSONArray jsonArray2_count = new JSONArray();
//		System.out.println(jsonArray2.length());
		jsonArray2_count.put(137).put(294).put(441).put(672).put(jsonArray2.length()-1);
//		jsonArray2_count.put(137).put(294).put(441).put(jsonArray2.length()-1);

		//2级CI
		JSONArray jsonArray15 = new JSONArray();
		
		//所有执行过的用例
		int allexeusecase=0;
		for(int i=0;i<jsonArray12.length();i++) {
			JSONObject tempJsonObject = jsonArray12.getJSONObject(i);
			String tempid = tempJsonObject.getString("id");
			String temphasautomated = tempJsonObject.getString("hasautomated");
			if(temphasautomated.equals("5-已部署")) {
				allexeusecase++;
			}
//			if(exeusecase.contains(tempid)) {
//				allexeusecase++;
//			}
		}
//		System.out.println("aa:"+allexeusecase);
		//7510用例
		String sql15="SELECT * FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ApplicableVersion like '%V5.85.10%'  and ptype=1 and  (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'";
//				+ " and ( performancetopic!='MU' and performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他' and performancetopic!='非特性组用例') 
		ResultSet rs15 = dao.executeQuery(sql15);

		try {
			jsonArray15 = ResultSetToJsonArray.resultSetToJsonArray(rs15);
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//特性组
//		int aaa = 0,bbb=0;
		JSONArray array = new JSONArray();
		String sql19 = "SELECT distinct performancetopic FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ApplicableVersion like '%V5.85.10%' and ptype=1  and  (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none'";
		ResultSet rs19 = dao.executeQuery(sql19);
		try {
			
			while(rs19.next()) {
				JSONObject object = new JSONObject();
				object.put("feature", rs19.getString(1));
				object.put("totalnum", 0);
				object.put("autonum", 0);
				object.put("autoratenum", 0);
				object.put("exeratenum", 0);
				object.put("exenum", 0);
				object.put("matchnum", 0);
				object.put("reportnum", 0);

				array.put(object);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		for(int i=0;i<jsonArray15.length();i++) {
			JSONObject object = jsonArray15.getJSONObject(i);
			String tempfeature = object.getString("performancetopic");
			String tempexecutephase = object.getString("executephase");
			String temphasautomated = object.getString("hasautomated");
			String tempid = object.getString("id");
			String hasreport = object.getString("hasreport");

			String tempExecuteFrequency = object.getString("ExecuteFrequency");

			String tempismatch = object.getString("envismatch");

//			String tempversion = object.getString("TestCaseApplicableVersion");
			for(int j=0;j<array.length();j++) {
				JSONObject obj = array.getJSONObject(j);
				if(obj.getString("feature").equals(tempfeature)) {
//					if(tempfeature.equals("语音性能")||tempfeature.equals("KPI")||tempfeature.equals("TNR基础性能")||tempfeature.equals("高铁")||tempfeature.equals("IOT")) {
						if(temphasautomated.contains("已部署")) {
							obj.put("exeratenum", obj.getInt("exeratenum")+1) ;
							allexeusecase++;
							if(!tempExecuteFrequency.equals("")) {
								obj.put("exenum", obj.getInt("exenum")+Integer.valueOf(tempExecuteFrequency)) ;
							}
						}
					obj.put("totalnum", obj.getInt("totalnum")+1) ;
					if(!temphasautomated.equals("1-无法自动化")) {
						obj.put("autonum", obj.getInt("autonum")+1) ;
						if(tempismatch.equals("2")) {
							obj.put("matchnum", obj.getInt("matchnum")+1) ;
						}
					}
					if(hasreport.equals("1")) {
						obj.put("reportnum", obj.getInt("reportnum")+1) ;
					}
				}
			}
		}
		for(int i=0;i<array.length();i++) {
			int a = array.getJSONObject(i).getInt("autonum");
			int d = array.getJSONObject(i).getInt("exeratenum");
			int total = array.getJSONObject(i).getInt("totalnum");
			
			int c = array.getJSONObject(i).getInt("matchnum");
			
			int e = array.getJSONObject(i).getInt("reportnum");
			if(e>=d) {
				 array.getJSONObject(i).put("reportnum", d) ;
				 e= d;
			}

			if(a==0||total==0)
				array.getJSONObject(i).put("autorate",0);
			else{
				array.getJSONObject(i).put("autorate",(double)a/(double)total );
			}
			if(a==0||d==0)
				array.getJSONObject(i).put("exerate",0);
			else{
				array.getJSONObject(i).put("exerate",(double)d/(double)a );
			}
			
			if(a==0||c==0)
				array.getJSONObject(i).put("matchrate",0);
			else{
				array.getJSONObject(i).put("matchrate",(double)c/(double)a );
			}
			
			if(e==0||d==0)
				array.getJSONObject(i).put("reportrate",0);
			else{
				array.getJSONObject(i).put("reportrate",(double)e/(double)d );
			}
		}

		for(int i=0;i<array.length();i++) {
			JSONObject obj = array.getJSONObject(i);
//			obj.put("autorate", String.format("%.2f", Double.valueOf(obj.getDouble("autorate"))*100) +"%") ;
			obj.put("autorate", String.format("%.2f", Double.valueOf(obj.getDouble("autorate"))*100)) ;
			obj.put("exerate", String.format("%.2f", Double.valueOf(obj.getDouble("exerate"))*100)) ;
			obj.put("matchrate", String.format("%.2f", Double.valueOf(obj.getDouble("matchrate"))*100)) ;
			obj.put("reportrate", String.format("%.2f", Double.valueOf(obj.getDouble("reportrate"))*100)) ;

		}
		
		
		
		//团队
		JSONArray teamarray = new JSONArray();
		String sql20 = "SELECT distinct team FROM rdctestcase where updatetime >= DATE_SUB(NOW(), INTERVAL 24 HOUR) and (state='就绪' or state='新建') and  ApplicableVersion like '%V5.85.10%' and ptype=1 and  (mrids!='' or featureids!='') and (executephase='FRT' or executephase='IOT') and performancetopic!='none' order by team";
//				+ "and (performancetopic!='none' and  performancetopic!='TOB' and performancetopic!='其他' and performancetopic!='MU' and performancetopic!='非特性组用例') 
		ResultSet rs20 = dao.executeQuery(sql20);
		try {
			
			while(rs20.next()) {
				JSONObject object = new JSONObject();
				object.put("team", rs20.getString(1));
				object.put("totalnum", 0);
				object.put("autonum", 0);
				object.put("autoratenum", 0);
				object.put("exeratenum", 0);
				teamarray.put(object);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		for(int i=0;i<jsonArray15.length();i++) {
			JSONObject object = jsonArray15.getJSONObject(i);
			String tempfeature = object.getString("team");
			String tempexecutephase = object.getString("executephase");
			String temphasautomated = object.getString("hasautomated");
			String tempid = object.getString("id");

//			String tempversion = object.getString("TestCaseApplicableVersion");
			for(int j=0;j<teamarray.length();j++) {
				JSONObject obj = teamarray.getJSONObject(j);
				if(obj.getString("team").equals(tempfeature)) {
//					if(tempfeature.equals("电联FOA")||tempfeature.equals("中移FOA")||tempfeature.equals("IOT")) {
						if(temphasautomated.contains("已部署")) {
							obj.put("exeratenum", obj.getInt("exeratenum")+1) ;
						}
//					}else {
//						if(exeusecase.contains(tempid)) {
//							obj.put("exeratenum", obj.getInt("exeratenum")+1) ;
//						}
//					}
					obj.put("totalnum", obj.getInt("totalnum")+1) ;
					if(!temphasautomated.equals("1-无法自动化")) {
						obj.put("autonum", obj.getInt("autonum")+1) ;
					}
				}
			}
		}

		for(int i=0;i<teamarray.length();i++) {
			int a = teamarray.getJSONObject(i).getInt("autonum");
			int d = teamarray.getJSONObject(i).getInt("exeratenum");
			int total = teamarray.getJSONObject(i).getInt("totalnum");
			if(a==0||total==0)
				teamarray.getJSONObject(i).put("autorate",0);
			else{
				teamarray.getJSONObject(i).put("autorate",(double)a/(double)total );
			}
			if(a==0||d==0)
				teamarray.getJSONObject(i).put("exerate",0);
			else{
				teamarray.getJSONObject(i).put("exerate",(double)d/(double)a );
			}
		}

		for(int i=0;i<teamarray.length();i++) {
			JSONObject obj = teamarray.getJSONObject(i);
//			obj.put("autorate", String.format("%.2f", Double.valueOf(obj.getDouble("autorate"))*100) +"%") ;
			obj.put("autorate", String.format("%.2f", Double.valueOf(obj.getDouble("autorate"))*100)) ;
			obj.put("exerate", String.format("%.2f", Double.valueOf(obj.getDouble("exerate"))*100)) ;

//			obj.put("huiguiautorate", String.format("%.2f", Double.valueOf(obj.getDouble("huiguiautorate"))*100)) ;
		}
		
		//iChangeEC
		JSONObject objectIchange = new JSONObject();
		objectIchange.put("ci1",0).put("ci2",0).put("ci3feature",0).put("ci3maoyan",0).put("iwork",0);

		String sql_iChange1="SELECT count(*) FROM iwork2.ichange_ec where type='1CI';";
		String sql_iChange2="SELECT count(*) FROM iwork2.ichange_ec where type='2CI';";
		String sql_iChange3="SELECT count(*) FROM iwork2.ichange_ec where type='3CIfeature';";
		String sql_iChange4="SELECT count(*) FROM iwork2.ichange_ec where type='3CImaoyan';";
		String sql_iChange5="SELECT count(*) FROM iwork2.ichange_ec where type='iwork';";
		
		ResultSet rs_iChange1 = dao.executeQuery(sql_iChange1);
		ResultSet rs_iChange2 = dao.executeQuery(sql_iChange2);
		ResultSet rs_iChange3 = dao.executeQuery(sql_iChange3);
		ResultSet rs_iChange4 = dao.executeQuery(sql_iChange4);
		ResultSet rs_iChange5 = dao.executeQuery(sql_iChange5);
		
		try {
			if(rs_iChange1.next()) {
				objectIchange.put("ci1", rs_iChange1.getInt(1));
			}
			if(rs_iChange2.next()) {
				objectIchange.put("ci2", rs_iChange2.getInt(1));
			}
			if(rs_iChange3.next()) {
				objectIchange.put("ci3feature", rs_iChange3.getInt(1));
			}
			if(rs_iChange4.next()) {
				objectIchange.put("ci3maoyan", rs_iChange4.getInt(1));
			}
			if(rs_iChange5.next()) {
				objectIchange.put("iwork", rs_iChange5.getInt(1));
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		JSONObject object = new JSONObject();
//		String success = String.format("%.2f", (double)successactusecasenum/(double)actusecasenum * 100) ;
		String actrate = String.format("%.2f", (double)actusecasenum/(double)allusecase * 100) ;
//		String frtactrate = String.format("%.2f", (double)frtactusecase/(double)frtallusecase * 100) ;
		String exerate = String.format("%.2f", (double)allexeusecase/(double)allautousecase * 100) ;

		object.put("countjob", countjob).put("sumactnum", sumactnum).put("allusecase", allusecase).
		put("ptype0", ptype0).put("ptype1", ptype1).put("ptype2", ptype2).put("ptype3", ptype3)
		.put("actusecasenum", actrate+"%").put("frtactrate", "100%").put("exerate", exerate+"%")
		.put("actusecasenum0", actusecasenum0).put("actusecasenum1", actusecasenum1).put("actusecasenum2", actusecasenum2).put("actusecasenum3", actusecasenum3);
		result.put(object);
		result.put(jsonArray_title);
		JSONArray jsonArray = new JSONArray();
		result.put(jsonArray);
		result.put(jsonArray_0);
		result.put(jsonArray_1);
		result.put(jsonArray_2);
		result.put(jsonArray_3);
//		System.out.println(jsonArray_title);
//		System.out.println(jsonArray_0);
//		System.out.println(jsonArray_1);
//		System.out.println(jsonArray_2);
//		System.out.println(jsonArray_3);

		result.put(jsonArray_title2);
		result.put(jsonArray2);

		result.put(array);
//		System.out.println(array);
		result.put(objectIchange);
		
		result.put(jsonArray2_count);
		
		result.put(teamarray);

		dao.close();
		PrintWriter out = response.getWriter(); 
		out.print(result);
		out.flush();
		out.close();
	}

	private JSONArray queryusecasebyptype(JSONArray jsonArray_title, String ptype) {
		JSONArray array = new JSONArray();
		DaoCMCC dao = new DaoCMCC();
		for(int i=0;i<jsonArray_title.length();i++) {
			array.put(0);
		}
		String sql6="SELECT DATE_FORMAT(testtime,'%Y%-%m%-%d') days,count(usecasename) as count FROM jobtestinfo  where ptype='"+ptype+"' and DATE_SUB(CURDATE(), INTERVAL 6 DAY) <= date(testtime)  group by days";
		ResultSet rs6 = dao.executeQuery(sql6);
		try {
			while(rs6.next()) {
				String tempdate = rs6.getString(1);
				for(int i=0;i<jsonArray_title.length();i++) {
					if(jsonArray_title.getString(i).equals(tempdate)) {
						array.put(i,rs6.getInt(2));
					}
				}			
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		dao.close();
		return array;
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




