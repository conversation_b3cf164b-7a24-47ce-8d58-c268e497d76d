package servlet2;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.net.ssl.SSLContext;
import javax.security.cert.X509Certificate;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;

import dao.DaoCMCC;
import dao.ResultSetToJsonArray;


/**
 * Servlet implementation class QueryHWInfoServlet
 */
@WebServlet("/QueryCiDataTableServlet")
public class QueryCiDataTableServlet extends HttpServlet {
	 private static final long serialVersionUID = 1L;
	 private HttpClient customHttpClient=null;  
    /**
     * @see HttpServlet#HttpServlet()
     */
    public QueryCiDataTableServlet() {
        super();
        // TODO Auto-generated constructor stub
    }
    
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request,response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setContentType("text/html");
		request.setCharacterEncoding("UTF-8");  
		response.setCharacterEncoding("UTF-8");
		
		DaoCMCC dao = new DaoCMCC();
		
		String page = request.getParameter("pageIndex");
		String limit = request.getParameter("pageSize");
		String systemid = request.getParameter("systemid");


		int limit2 = Integer.valueOf(limit);
		int limit1 =(Integer.valueOf(page)-1)*limit2; 
		

		JSONArray jsonArray0 = new JSONArray();
		systemid = "ZXPFM-"+systemid;
		String jobset = "";

        String Sql="select jobset from  rdctestcase where systemid ='"+systemid+"'";
        ResultSet rs = dao.executeQuery(Sql);

        try {
			if(rs.next()) {
				if(rs.getString(1)==null) {
					
				}else {
					jobset = rs.getString(1);
				}
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        if(jobset.equals("")) {
        	
        }else {
        	jsonArray0 = new JSONArray(jobset);
    		for(int i=0;i<jsonArray0.length();i++) {
    			//创建任务
    			JSONObject teJsonObject = jsonArray0.getJSONObject(i);
    			String id = teJsonObject.getString("jobid");
    			String sql0="SELECT jobname,kpitemplate,ifnew,creattime FROM iwork2.job where id='"+id+"';";
    			ResultSet rs0 = dao.executeQuery(sql0);
    			String jobname = "";
    			String kpitemplate = "";
       			String ifnew = "";
    			String creattime = "";
    			try {
    				if(rs0.next()) {
    					jobname = rs0.getString(1);
    					kpitemplate = rs0.getString(2);
    					ifnew = rs0.getString(3);
    					creattime = rs0.getString(4);
    				}
    			} catch (SQLException e) {
    				// TODO Auto-generated catch block
    				e.printStackTrace();
    			}
    			teJsonObject.put("jobname",jobname);
    			teJsonObject.put("kpitemplate",kpitemplate);
    			teJsonObject.put("creattime",creattime);
    			teJsonObject.put("ifnew",ifnew);

    		}
        }

		JSONArray jsonArray1 = new JSONArray();
        if(jsonArray0.length()>0) {
        	for(int i=0;i<jsonArray0.length();i++) {
    			JSONObject teJsonObject = jsonArray0.getJSONObject(i);
    			if(!teJsonObject.isNull("ifnew")) {
    				if(teJsonObject.getString("ifnew").equals("1")) {
    					jsonArray1.put(teJsonObject);
    				}
    			}
        	}
        }
        
        if(jsonArray0.length()>0) {
        	for(int i=0;i<jsonArray0.length();i++) {
    			JSONObject teJsonObject = jsonArray0.getJSONObject(i);
    			if(!teJsonObject.isNull("ifnew")) {
    				if(teJsonObject.getString("ifnew").equals("0")) {
    					jsonArray1.put(teJsonObject);
    				}
    			}
        	}
        }
		
		

		JSONObject object = new JSONObject();
		object.put("code", 0).put("msg", "");
		object.put("count", jsonArray1.length());
		object.put("data", jsonArray1);
		dao.close();

		PrintWriter out = response.getWriter(); 
		out.print(object);
		out.flush();
		out.close();
	}

	public static String getRadomFileName(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Date date = new Date();
		String str = simpleDateFormat.format(date);
	
		Random random = new Random();
		int rannum = (int)(random.nextDouble()*(99999999-10000000+1))+10000000;
	
		return rannum+str;
	}


	protected void intihttpclient() { //锟斤拷始锟斤拷http锟酵伙拷锟剿ｏ拷锟截憋拷SSL锟斤拷权
		try {
	        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustSelfSignedStrategy() {
	            public boolean isTrusted(X509Certificate[] chain, String authType) {
	                return true;
	            }
	        }).build();
	        customHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
	        Unirest.setHttpClient(customHttpClient);
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	
	protected String logintoken(JSONObject body,String url) {
		
		try {
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.post(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	                .body(body.toString())
	                .asJson(); 
		     //System.out.println(httpresponse.getBody().toString());
		     return httpresponse.getBody().getObject().getString("access_token");
	    } catch (Exception e) {
	    	 return "-1";  //锟斤拷取tocken失锟斤拷
	    }	
		
	}
	
	protected void logouttoken(String token,String url) {
		
		try {	       
	        Unirest.setHttpClient(customHttpClient);
	        HttpResponse<JsonNode> httpresponse = Unirest.get(url)
	        		.header("content-type", "application/json")
	        	    .header("accept", "application/json")
	        	    .header("Z-ACCESS-TOKEN",token)
	                .asJson(); 
		     //System.out.println(httpresponse.getHeaders().toString());
	    } catch (Exception e) {
	    	 e.printStackTrace();
	    }
	}
}




